<?php

namespace Modules\User\Enums;

use App\Traits\EnumMethods;

enum IdentityOrderStatus: string
{
    use EnumMethods;

    case UNPAY  = 'unpay';
    case PAID   = 'paid';
    case SETTLE = 'settle';

    const STATUS_MAP = [
        self::UNPAY->value  => '未支付',
        self::PAID->value   => '已支付',
        self::SETTLE->value => '已核算',
    ];

    const STATUS_LABEL = [
        self::UNPAY->value  => 'primary',
        self::PAID->value   => 'success',
        self::SETTLE->value => 'danger',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}
