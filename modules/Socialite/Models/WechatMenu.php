<?php

namespace Modules\Socialite\Models;

use App\Models\Model;
use Dcat\Admin\Traits\ModelTree;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Socialite\Enums\WechatMenuType;
use Spatie\EloquentSortable\Sortable;

class WechatMenu extends Model implements Sortable
{
    use ModelTree;

    protected $table = 'socialite_wechat_menus';

    protected $titleColumn = 'name';

    protected $sortable = [
        'order_column_name'  => 'order',
        'sort_when_creating' => true,
    ];

    protected $casts = [
        'type'   => WechatMenuType::class,
        'params' => 'json',
    ];

    /**
     * Notes   : 上级菜单
     *
     * @Date   : 2023/5/26 09:33
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class);
    }

    /**
     * Notes   : 发布菜单时用的
     *
     * @Date   : 2023/5/26 09:32
     * <AUTHOR> <Jason.C>
     * @return array
     */
    public function toMenuArray(): array
    {
        $menu = [];

        if ($this->children()->exists()) {
            foreach ($this->children as $child) {
                $menu['name']         = $this->name;
                $menu['sub_button'][] = $this->parseMenu($child);
            }
        } else {
            $menu = $this->parseMenu($this);
        }

        return $menu;
    }

    /**
     * Notes   : 子菜单
     *
     * @Date   : 2023/5/26 09:33
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    private function parseMenu(WechatMenu $menu): array
    {
        $base = [
            'name' => $menu->name,
            'type' => $menu->type->value,
        ];

        switch ($menu->type) {
            case WechatMenuType::CLICK:
                $base['key'] = $menu->params['key'];
                break;
            case WechatMenuType::VIEW:
                $base['url'] = $menu->params['url'];
                break;
            case WechatMenuType::MINI_PROGRAM:
                $base['url']      = $menu->params['failed_url'];
                $base['appid']    = $menu->params['appid'];
                $base['pagepath'] = $menu->params['pagepath'];
                break;
            case WechatMenuType::SCANCODE_PUSH:
                $base['key'] = $menu->params['scancode_push_key'];
                break;
            case WechatMenuType::PIC_SYSPHOTO:
                $base['key'] = $menu->params['pic_sysphoto_key'];
                break;
            case WechatMenuType::PIC_WEIXIN:
                $base['key'] = $menu->params['pic_weixin_key'];
                break;
            case WechatMenuType::PIC_PHOTO_OR_ALBUM:
                $base['key'] = $menu->params['pic_photo_or_album_key'];
                break;
            case WechatMenuType::LOCATION_SELECT:
                $base['key'] = $menu->params['location_key'];
                break;
        }

        return $base;
    }
}
