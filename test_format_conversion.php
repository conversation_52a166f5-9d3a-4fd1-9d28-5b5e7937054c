<?php

// 测试豆包返回格式转换

$doubaoResponse = '### 会议主题  
| 会议时间 | 会议主题 | 会议时长 |  
|----------|----------|----------|  
| 2025-06-26 10:00:00 - 2025-06-26 11:00:00 | 项目进度讨论 | 60分钟 |  


### 内容总结
会议围绕项目进度展开讨论，各相关方分别汇报了前端开发、后端优化及测试安排的进展情况，明确了各项任务的时间节点。


### 会议总结
本次会议聚焦项目进度协调，明确了前端开发、后端优化及测试工作的具体时间规划，为后续工作推进提供了时间依据。


### 代办事项
1. **前端团队**：下周完成剩余20%开发任务
2. **后端团队**：3天内完成API接口优化
3. **测试团队**：准备测试用例，下周二启动测试工作


### 章节摘要

| 讲话人 | 讲话时长 | 讲话内容摘要 |
|--------|----------|--------------|
| 1 | 4秒 | 宣布会议主题为讨论项目进度 |
| 2 | 6秒 | 前端开发已完成80%，预计下周完成 |
| 1 | 5秒 | 后端API接口需优化，预计耗时3天 |
| 3 | 6秒 | 测试工作安排在下周二开始，提醒准备测试用例 |';

echo "=== 豆包格式转换测试 ===\n\n";

// 解析函数
function parseDoubaoResponse(string $content): array
{
    $sections = [
        'meeting_topics' => '',
        'content' => '',
        'meeting_summary' => '',
        'todo' => '',
        'chapter_summary' => ''
    ];

    // 解析会议主题部分（包含表格）
    if (preg_match('/### 会议主题\s*(.*?)(?=###|$)/s', $content, $matches)) {
        $sections['meeting_topics'] = trim($matches[1]);
    }

    // 解析内容总结
    if (preg_match('/### 内容总结\s*(.*?)(?=###|$)/s', $content, $matches)) {
        $sections['content'] = trim($matches[1]);
    }

    // 解析会议总结
    if (preg_match('/### 会议总结\s*(.*?)(?=###|$)/s', $content, $matches)) {
        $sections['meeting_summary'] = trim($matches[1]);
    }

    // 解析代办事项
    if (preg_match('/### 代办事项\s*(.*?)(?=###|$)/s', $content, $matches)) {
        $sections['todo'] = trim($matches[1]);
    }

    // 解析章节摘要
    if (preg_match('/### 章节摘要\s*(.*?)(?=###|$)/s', $content, $matches)) {
        $sections['chapter_summary'] = trim($matches[1]);
    }

    return $sections;
}

// 格式化函数
function buildMarkdownSummary(array $summaryData): string
{
    $markdown = "## 会议信息\n\n";
    
    // 处理会议主题信息，转换表格为文本格式
    $meetingInfo = $summaryData['meeting_topics'];
    if (strpos($meetingInfo, '|') !== false) {
        // 提取表格第二行数据
        $lines = explode("\n", $meetingInfo);
        foreach ($lines as $line) {
            if (preg_match('/\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|/', $line) && 
                !strpos($line, '会议时间')) {
                $parts = explode('|', $line);
                if (count($parts) >= 4) {
                    $time = trim($parts[1]);
                    $topic = trim($parts[2]);
                    $duration = trim($parts[3]);
                    
                    $markdown .= "• **会议时间**: {$time}\n";
                    $markdown .= "• **会议主题**: {$topic}\n";
                    $markdown .= "• **会议时长**: {$duration}\n";
                    $markdown .= "• **参与人数**: 约3人\n";
                    $markdown .= "• **场景类型**: 项目讨论\n\n";
                    break;
                }
            }
        }
    }

    $markdown .= "## 会议总结\n\n";
    $markdown .= trim($summaryData['meeting_summary']) . "\n\n";

    $markdown .= "## 会议要点\n\n";
    $markdown .= "- " . trim($summaryData['content']) . "\n\n";

    $markdown .= "## 章节摘要\n\n";
    
    // 转换章节摘要表格
    $chapterSummary = $summaryData['chapter_summary'];
    $lines = explode("\n", $chapterSummary);
    $chapterNum = 1;
    
    foreach ($lines as $line) {
        if (preg_match('/\|\s*(\d+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|/', $line)) {
            $parts = explode('|', $line);
            if (count($parts) >= 4) {
                $speaker = trim($parts[1]);
                $duration = trim($parts[2]);
                $summary = trim($parts[3]);
                
                $markdown .= "##### {$chapterNum} 发言人{$speaker}\n\n";
                $markdown .= "{$summary}\n\n";
                $chapterNum++;
            }
        }
    }

    $markdown .= "## 待办事项\n\n";
    
    // 转换待办事项
    $todoLines = explode("\n", $summaryData['todo']);
    foreach ($todoLines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // 转换数字列表为破折号列表
        $line = preg_replace('/^\d+\.\s*\*\*([^*]+)\*\*:\s*/', '- **$1**: ', $line);
        $markdown .= $line . "\n";
    }

    return $markdown;
}

// 测试解析
echo "1. 解析豆包响应...\n";
$parsed = parseDoubaoResponse($doubaoResponse);

foreach ($parsed as $key => $value) {
    echo "- {$key}: " . (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value) . "\n";
}

echo "\n2. 转换为统一格式...\n";
$markdown = buildMarkdownSummary($parsed);

echo "转换后的markdown:\n";
echo "===================\n";
echo $markdown;
echo "===================\n";

echo "\n=== 测试完成 ===\n";
