<?php

namespace App\Console\Commands;

use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Models\BailianArticle;
use App\Services\MeetingSummaryService;
use Illuminate\Console\Command;

class TestDoubaoIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:doubao';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试豆包AI会议总结集成';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始测试豆包AI会议总结集成...');

        // 创建测试会议笔记
        $testData = [
            'audio' => 'https://cdn.watestar.com/2025/06/25/test.wav',
            'audioDuration' => 300,
            'text_record' => json_encode([
                [
                    'start' => 1000,
                    'end' => 5000,
                    'text' => '大家好，今天我们开会讨论项目进度。',
                    'speaker' => 1
                ],
                [
                    'start' => 6000,
                    'end' => 12000,
                    'text' => '目前前端开发已经完成了80%，预计下周可以完成。',
                    'speaker' => 2
                ],
                [
                    'start' => 13000,
                    'end' => 18000,
                    'text' => '后端API接口还需要优化，预计需要3天时间。',
                    'speaker' => 1
                ],
                [
                    'start' => 19000,
                    'end' => 25000,
                    'text' => '测试工作安排在下周二开始，大家准备好测试用例。',
                    'speaker' => 3
                ]
            ]),
            'meeting_info' => [
                'start_at' => '2025-06-26 10:00:00',
                'end_at' => '2025-06-26 11:00:00',
                'members' => 3
            ]
        ];

        $article = BailianArticle::create([
            'title' => '测试会议记录 - ' . date('Y-m-d H:i:s'),
            'content' => $testData,
            'type' => BailianArticleTypeEnum::MEETING,
            'user_id' => 1,
        ]);

        $this->info("创建测试文章，ID: {$article->id}");

        // 测试豆包AI生成
        $service = new MeetingSummaryService();
        $result = $service->generateSummaryWithDoubao($article);

        if ($result['success']) {
            $this->info('✅ 豆包AI生成成功！');

            // 刷新文章数据
            $article->refresh();

            $this->info('文章标题: ' . $article->title);
            $this->info('处理状态: ' . $article->processing_status->value);
            $this->info('内容长度: ' . strlen($article->content['data'] ?? ''));

            // 显示生成的内容预览
            $content = $article->content['data'] ?? '';
            $this->line('生成的内容预览:');
            $this->line('==================');
            $this->line(substr($content, 0, 500) . '...');
            $this->line('==================');

            // 测试getMeetingSummaryStatus
            $status = $article->getMeetingSummaryStatus();
            $this->info('会议总结状态:');
            $this->table(['字段', '值'], [
                ['has_summary', $status['has_summary'] ? '是' : '否'],
                ['has_agents', $status['has_agents'] ? '是' : '否'],
                ['has_paragraphs', $status['has_paragraphs'] ? '是' : '否'],
            ]);

        } else {
            $this->error('❌ 豆包AI生成失败: ' . $result['message']);
        }

        // 清理测试数据
        $article->delete();
        $this->info('测试完成，已清理测试数据');
    }
}
