<?php

namespace App\Console\Commands;

use App\Models\BailianArticle;
use Illuminate\Console\Command;

class CheckArticleStatusCommand extends Command
{
    protected $signature = 'check:article-status {id}';
    protected $description = '检查文章处理状态';

    public function handle()
    {
        $id = $this->argument('id');
        $article = BailianArticle::find($id);
        
        if (!$article) {
            $this->error("文章 ID {$id} 不存在");
            return;
        }

        $this->info("文章 ID: {$article->id}");
        $this->info("标题: {$article->title}");
        $this->info("处理状态: {$article->processing_status->value}");
        $this->info("开始时间: " . ($article->processing_started_at ?? '未开始'));
        $this->info("完成时间: " . ($article->processing_completed_at ?? '未完成'));
        $this->info("错误信息: " . ($article->processing_error ?? '无'));

        if ($article->processing_status->value === 'completed') {
            $content = $article->content['data'] ?? '';
            $this->info("生成内容长度: " . strlen($content));
            
            if ($content) {
                $this->line('内容预览:');
                $this->line('==================');
                $this->line(substr($content, 0, 300) . '...');
                $this->line('==================');
            }

            // 检查状态
            $status = $article->getMeetingSummaryStatus();
            $this->info('会议总结状态:');
            $this->table(['字段', '值'], [
                ['has_summary', $status['has_summary'] ? '是' : '否'],
                ['has_agents', $status['has_agents'] ? '是' : '否'],
                ['has_paragraphs', $status['has_paragraphs'] ? '是' : '否'],
            ]);
        }
    }
}
