<?php

namespace App\Console\Commands;

use App\Services\MeetingSummaryService;
use Illuminate\Console\Command;

class TestDoubaoApiCommand extends Command
{
    protected $signature = 'test:doubao-api';
    protected $description = '测试豆包AI API调用';

    public function handle()
    {
        $this->info('开始测试豆包AI API调用...');

        // 创建测试数据
        $testTextRecord = json_encode([
            [
                "start" => 1000,
                "end" => 5000,
                "text" => "大家好，今天我们来讨论一下项目的进展情况。",
                "speaker" => 1
            ],
            [
                "start" => 5000,
                "end" => 10000,
                "text" => "好的，我来汇报一下前端开发的情况。目前我们已经完成了用户界面的设计。",
                "speaker" => 2
            ],
            [
                "start" => 10000,
                "end" => 15000,
                "text" => "后端这边，我们已经完成了API接口的开发，还需要进行测试。",
                "speaker" => 3
            ],
            [
                "start" => 15000,
                "end" => 20000,
                "text" => "那我们下周的计划是什么？需要完成哪些任务？",
                "speaker" => 1
            ],
            [
                "start" => 20000,
                "end" => 25000,
                "text" => "前端需要完成组件的集成，后端需要完成API测试。",
                "speaker" => 2
            ]
        ]);

        $meetingInfo = [
            'start_at' => '2025-06-26 10:00:00',
            'end_at' => '2025-06-26 11:00:00',
            'members' => 3
        ];

        $service = new MeetingSummaryService();

        // 构建提示词
        $prompt = $this->callPrivateMethod($service, 'buildDoubaoPrompt', [$testTextRecord, $meetingInfo]);
        
        $this->info('构建的提示词:');
        $this->line('==================');
        $this->line($prompt);
        $this->line('==================');

        // 调用豆包API
        $this->info('正在调用豆包API...');
        $result = $this->callPrivateMethod($service, 'makeDoubaoApiCall', [$prompt]);

        if ($result['success']) {
            $this->info('✅ 豆包API调用成功！');
            $this->info('返回内容长度: ' . strlen($result['content']));
            
            $this->line('返回内容预览:');
            $this->line('==================');
            $this->line(substr($result['content'], 0, 1000));
            $this->line('==================');

            // 测试解析
            $this->info('正在解析响应...');
            $parsed = $this->callPrivateMethod($service, 'parseDoubaoResponse', [$result['content']]);
            
            $this->info('解析结果:');
            foreach ($parsed as $key => $value) {
                $preview = substr($value, 0, 100);
                $this->line("{$key}: " . ($preview ? $preview . '...' : '(空)'));
            }
        } else {
            $this->error('❌ 豆包API调用失败: ' . $result['message']);
            if (isset($result['response'])) {
                $this->line('响应内容: ' . $result['response']);
            }
        }
    }

    /**
     * 调用私有方法的辅助函数
     */
    private function callPrivateMethod($object, $methodName, $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        return $method->invokeArgs($object, $parameters);
    }
}
