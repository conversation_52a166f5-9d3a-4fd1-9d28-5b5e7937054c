<?php

namespace App\Console\Commands;

use App\Enums\Bailian\ArticleProcessingStatusEnum;
use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Jobs\BaiLian\GenerateDoubaoSummaryJob;
use App\Models\BailianArticle;
use Illuminate\Console\Command;

class TestAsyncDoubaoCommand extends Command
{
    protected $signature = 'test:async-doubao';
    protected $description = '测试豆包AI异步处理流程';

    public function handle()
    {
        $this->info('开始测试豆包AI异步处理流程...');

        // 创建测试会议笔记
        $testData = [
            'text_record' => json_encode([
                [
                    "start" => 1000,
                    "end" => 5000,
                    "text" => "大家好，今天我们来讨论一下项目的进展情况。",
                    "speaker" => 1
                ],
                [
                    "start" => 5000,
                    "end" => 10000,
                    "text" => "好的，我来汇报一下前端开发的情况。目前我们已经完成了用户界面的设计。",
                    "speaker" => 2
                ],
                [
                    "start" => 10000,
                    "end" => 15000,
                    "text" => "后端这边，我们已经完成了API接口的开发，还需要进行测试。",
                    "speaker" => 3
                ],
                [
                    "start" => 15000,
                    "end" => 20000,
                    "text" => "那我们下周的计划是什么？需要完成哪些任务？",
                    "speaker" => 1
                ],
                [
                    "start" => 20000,
                    "end" => 25000,
                    "text" => "前端需要完成组件的集成，后端需要完成API测试。",
                    "speaker" => 2
                ]
            ]),
            'meeting_info' => [
                'start_at' => '2025-06-26 10:00:00',
                'end_at' => '2025-06-26 11:00:00',
                'members' => 3
            ]
        ];

        $article = BailianArticle::create([
            'title' => '测试异步会议记录 - ' . date('Y-m-d H:i:s'),
            'content' => $testData,
            'type' => BailianArticleTypeEnum::MEETING,
            'user_id' => 1,
            'processing_status' => ArticleProcessingStatusEnum::PENDING,
        ]);

        $this->info("创建测试文章，ID: {$article->id}");

        // 提交异步任务
        dispatch(new GenerateDoubaoSummaryJob($article));
        $this->info('✅ 异步任务已提交到队列');

        // 处理队列任务
        $this->info('正在处理队列任务...');
        $this->call('queue:work', ['--once' => true]);

        // 检查结果
        $article->refresh();
        $this->info('处理完成，检查结果:');
        $this->info('处理状态: ' . $article->processing_status->value);
        
        if ($article->processing_status === ArticleProcessingStatusEnum::COMPLETED) {
            $this->info('✅ 处理成功！');
            $this->info('文章标题: ' . $article->title);
            
            $content = $article->content['data'] ?? '';
            $this->info('生成内容长度: ' . strlen($content));
            
            // 显示内容预览
            $this->line('生成的内容预览:');
            $this->line('==================');
            $this->line(substr($content, 0, 500) . '...');
            $this->line('==================');

            // 测试状态检查
            $status = $article->getMeetingSummaryStatus();
            $this->info('会议总结状态:');
            $this->table(['字段', '值'], [
                ['has_summary', $status['has_summary'] ? '是' : '否'],
                ['has_agents', $status['has_agents'] ? '是' : '否'],
                ['has_paragraphs', $status['has_paragraphs'] ? '是' : '否'],
            ]);
        } elseif ($article->processing_status === ArticleProcessingStatusEnum::FAILED) {
            $this->error('❌ 处理失败: ' . $article->processing_error);
        } else {
            $this->warn('⏳ 处理中...');
        }

        $this->info('测试完成！');
    }
}
