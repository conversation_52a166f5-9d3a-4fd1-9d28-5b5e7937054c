<?php

namespace App\Jobs\BaiLian;

use App\Enums\Bailian\ArticleProcessingStatusEnum;
use App\Jobs\BaseJob;
use App\Models\BailianArticle;
use App\Services\MeetingSummaryService;
use Exception;
use Illuminate\Support\Facades\Log;

class GenerateDoubaoSummaryJob extends BaseJob
{
    public string $queue = 'BaiLian';
    public int $timeout = 600; // 10分钟超时
    public int $tries = 2; // 尝试次数
    public int $backoff = 30; // 失败后等待时间（秒）

    /**
     * 创建一个新的任务实例
     *
     * @param BailianArticle $article
     */
    public function __construct(
        protected BailianArticle $article
    ) {}

    /**
     * 执行任务
     *
     * @return void
     * @throws Exception
     */
    public function handle(): void
    {
        try {
            // 刷新文章模型，确保获取最新状态
            $this->article->refresh();

            // 检查文章是否仍然存在
            if (!$this->article->exists) {
                Log::warning('豆包AI任务执行时文章已不存在', [
                    'article_id' => $this->article->id
                ]);
                return;
            }

            // 检查是否已经处理完成
            if ($this->article->processing_status === ArticleProcessingStatusEnum::COMPLETED) {
                Log::info('豆包AI任务执行时文章已完成处理', [
                    'article_id' => $this->article->id
                ]);
                return;
            }

            // 更新状态为处理中
            $this->article->update([
                'processing_status' => ArticleProcessingStatusEnum::PROCESSING,
                'processing_started_at' => now(),
                'processing_error' => null
            ]);

            Log::info('开始豆包AI会议总结生成', [
                'article_id' => $this->article->id,
                'attempt' => $this->attempts()
            ]);

            // 调用豆包AI服务
            $service = new MeetingSummaryService();
            $result = $service->generateSummaryWithDoubao($this->article);

            if ($result['success']) {
                Log::info('豆包AI会议总结生成成功', [
                    'article_id' => $this->article->id,
                    'attempt' => $this->attempts()
                ]);
            } else {
                // 豆包AI失败，标记为失败状态（不回退到讯飞）
                $this->article->update([
                    'processing_status' => ArticleProcessingStatusEnum::FAILED,
                    'processing_completed_at' => now(),
                    'processing_error' => $result['message'] ?? '豆包AI处理失败'
                ]);

                throw new Exception($result['message'] ?? '豆包AI处理失败');
            }

        } catch (Exception $e) {
            Log::error('豆包AI会议总结生成异常', [
                'article_id' => $this->article->id,
                'error' => $e->getMessage(),
                'attempt' => $this->attempts(),
                'trace' => $e->getTraceAsString()
            ]);

            // 更新错误状态
            $this->article->update([
                'processing_status' => ArticleProcessingStatusEnum::FAILED,
                'processing_completed_at' => now(),
                'processing_error' => $e->getMessage()
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 处理任务失败
     *
     * @param Exception $exception
     * @return void
     */
    public function failed(Exception $exception): void
    {
        Log::error('豆包AI会议总结任务最终失败', [
            'article_id' => $this->article->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // 确保状态被标记为失败
        try {
            $this->article->update([
                'processing_status' => ArticleProcessingStatusEnum::FAILED,
                'processing_completed_at' => now(),
                'processing_error' => '豆包AI处理最终失败: ' . $exception->getMessage()
            ]);
        } catch (Exception $e) {
            Log::error('更新文章失败状态时出错', [
                'article_id' => $this->article->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
