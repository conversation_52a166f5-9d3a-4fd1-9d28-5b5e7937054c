<?php

namespace App\Jobs\Plugin;

use App\Jobs\BaseJob;
use App\Models\PluginJmDraw;

class JmQueryJob extends BaseJob
{
    public string $queue = 'Query';

    public int $timeout = 120;

    public function __construct(protected PluginJmDraw $task)
    {
    }

    public function handle(): void
    {
        match ($this->task->type) {
            PluginJmDraw::TYPE_SYNC_LIP_IMAGE, PluginJmDraw::TYPE_SYNC_LIP_VIDEO => $this->task->bailianQuery(),
            PluginJmDraw::TYPE_NORMAL_VIDEO => $this->task->videoBigQuery(),
            default => $this->task->videoQuery(),
        };
    }
}