<?php

namespace App\MCP\Traits;

use Exception;
use <PERSON>uz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Str;

class AliCloudMarketClient
{
    protected Client $client;

    public function __construct(protected string $baseUri, protected string $appCode)
    {
        $this->client = new Client([
            'verify'   => false,
            'base_uri' => Str::finish($this->baseUri, '/'),
        ]);
    }

    public function get(string $path, array $query = [])
    {
        return $this->request('GET', $path, $query, 'query');
    }

    /**
     * @param  string  $method
     * @param  string  $path
     * @param  array  $params
     * @param  string  $paramsType
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    protected function request(string $method, string $path, array $params = [], string $paramsType = 'json')
    {
        try {
            $response = $this->client->request($method, $path, [
                'headers'   => $this->getHeader(),
                $paramsType => $params,
            ]);
            if ($response->getStatusCode() === 200) {
                return json_decode($response->getBody()->getContents(), true);
            } else {
                throw new Exception($response->getBody()->getContents().'--CODE:'.$response->getStatusCode());
            }
        } catch (RequestException $exception) {
            $content = $exception->getResponse()->getBody()->getContents();
            if (Str::isJson($content)) {
                $errData = json_decode($content, true);
                throw new Exception($errData['msg'] ?? '未知错误');
            }
            throw new Exception($content);
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage());
        }
    }

    protected function getHeader()
    {
        return [
            'Authorization' => 'APPCODE '.$this->appCode,
        ];
    }

    public function json(string $path, array $params = [])
    {
        return $this->request('POST', $path, $params, 'json');
    }

    public function post(string $path, array $params = [])
    {
        return $this->request('POST', $path, $params, 'form_params');
    }
}