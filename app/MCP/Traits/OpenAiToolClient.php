<?php

namespace App\MCP\Traits;

use OpenAI;
use OpenAI\Client;

class OpenAiToolClient
{
    protected Client $client;

    public function __construct(string $apiKey, string $baseUri)
    {
        $this->client = OpenAI::factory()
            ->withApiKey($apiKey)
            ->withBaseUri($baseUri)
            ->withHttpClient(new \GuzzleHttp\Client([
                'verify'  => false,
                'timeout' => 300,
            ]))
            ->make();
    }

    public function chatStream(
        string $modelId,
        string $system,
        string $prompt,
        int $maxTokens = 1024,
        array $other = []
    ): string {
        $params   = $this->getParams(
            $modelId,
            $system,
            $prompt,
            $maxTokens,
            $other
        );
        $response = $this->client->chat()->createStreamed($params);
        $content  = '';
        foreach ($response->getIterator() as $chunk) {
            $content .= $chunk->choices[0]->delta->content;
        }
        return $content;
    }

    protected function getParams(
        string $modelId,
        string $system,
        string $prompt,
        int $maxTokens = 1024,
        array $other = []
    ) {
        return array_merge([
            'model'      => $modelId,
            'messages'   => [
                [
                    'role'    => 'system',
                    'content' => $system,
                ],
                [
                    'role'    => 'user',
                    'content' => $prompt,
                ],
            ],
            'max_tokens' => $maxTokens,
        ], $other);
    }

    public function chat(
        string $modelId,
        string $system,
        string $prompt,
        int $maxTokens = 1024,
        array $other = []
    ): string {
        $params   = $this->getParams(
            $modelId,
            $system,
            $prompt,
            $maxTokens,
            $other
        );
        $response = $this->client->chat()->create($params);
        return $response->choices[0]->message->content;
    }
}