<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\MCP\Traits\OpenAiToolClient;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class GenPPTOutLineTool extends ToolsBase implements ToolInterface
{

    /**
     * Get the tool name.
     *
     * @return string
     */
    public function name(): string
    {
        return 'gen_ppt_outline';
    }

    /**
     * Get the tool description.
     *
     * @return string
     */
    public function description(): string
    {
        return '用户需要生成PPT大纲是调用此工具.';
    }

    /**
     * Get the input schema for the tool.
     *
     * @return array
     */
    public function inputSchema(): array
    {
        return [
            'type'       => 'object',//不用动
            'properties' => [
                'prompt' => [
                    'type'        => 'string',//类型
                    'description' => '对大纲内容的描述,如果引用了知识库文件则提供知识库文件的内容',
                ],
            ],
            'required'   => ['prompt'],//必填字段
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '生成PPT大纲'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        try {
            $prompt = $arguments['prompt'];
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }

        try {
            ignore_user_abort(true);
            set_time_limit(0);
            $system  = wateGetSystemPrompt('GenPPTOutLine');
            $client  = new OpenAiToolClient('d7559878-baec-4a6f-8d76-8d7d04a5582e',
                'https://ark.cn-beijing.volces.com/api/v3');
            $outline = $client->chatStream(
                'ep-20250611165125-7dhbg',
                $system,
                $prompt,
                16300,
                [
                    'thinking' => [
                        'type' => 'disabled'
                    ],
                ]
            );
            return $this->success([
                'outline' => $outline,
            ]);
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }
}
