<?php

namespace App\OfflineTask;

use App\Models\OfflineTask;
use App\Models\User;
use App\OfflineTask\BaseClass\BaseTask;
use Exception;
use Illuminate\Support\Str;

class OfflineTaskKernel
{
    protected bool        $taskStatus   = false;
    protected string      $errorMessage = '';
    private BaseTask|null $task;

    public function __construct(public string $command)
    {
        $this->regTask();
    }

    private function regTask()
    {
        $fileName = Str::finish($this->command, 'Task.php');
        if (file_exists(app_path('OfflineTask/Tools/'.$fileName))) {
            $className  = "App\\OfflineTask\\Tools\\".$this->command.'Task';
            $this->task = new $className;
        } else {
            $this->task = null;
        }
    }

    /**
     * @param  array  $params
     * @return \App\Models\OfflineTask
     */
    public function join(User $user, array $params): OfflineTask
    {
        $validateClass = "\\App\\OfflineTask\\Validations\\{$this->command}Validation";
        if (class_exists($validateClass)) {
            new $validateClass($params);
        }
        $result = $this->task->canJoin($user, $params);
        if ($result === true) {
            $data     = [
                'user_id' => $user->id,
                'command' => $this->command,
                'name'    => $this->task->getTaskName(),
                'remark'  => $this->task->getTaskRemark($params),
                'minutes' => $this->task->getMinutes(),
                'params'  => $params,
                'status'  => OfflineTask::STATUS_ING,
                'next_at' => now()->addMinutes($this->task->getMinutes()),
                'last_at' => null,
            ];
            $taskItem = OfflineTask::create($data);
            return $taskItem;
        } else {
            throw new Exception($result);
        }
    }

    public function run(OfflineTask $task): void
    {
        try {
            if ($this->hasTask()) {
                $this->task->setTask($task)->run();
            } else {
                throw new Exception('任务不存在');
            }
        } catch (Exception $exception) {
            $this->taskStatus   = false;
            $this->errorMessage = $exception->getMessage();
        }
    }

    public function hasTask(): bool
    {
        return (bool) $this->task;
    }

    public function getErrorMessage(): string
    {
        if ($this->task->isSuccess() === false) {
            return $this->task->getMessage();
        }
        return $this->errorMessage;
    }

    public function isSuccess(): bool
    {
        return $this->taskStatus && $this->task->isSuccess();
    }

    public function __call(string $name, array $arguments)
    {
        return $this->task->{$name}(...$arguments);
    }

}