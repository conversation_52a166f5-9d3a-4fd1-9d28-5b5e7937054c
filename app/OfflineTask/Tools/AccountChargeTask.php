<?php

namespace App\OfflineTask\Tools;

use App\Models\OfflineTask;
use App\Models\User;
use App\OfflineTask\BaseClass\BaseTask;
use Exception;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Models\AccountLog;

class AccountChargeTask extends BaseTask
{
    public string    $name    = 'AccountCharge';
    public int       $minutes = 20;
    protected string $cnName  = '账户变动监测';

    public function canJoin(User $user, array $params)
    {
        $task = OfflineTask::ofUser($user)
            ->where('status', OfflineTask::STATUS_ING)
            ->where('command', $this->name)
            ->first();
        if ($task) {
            if ($task->params['account'] != $params['account']) {
                return true;
            }
            if ($task->params['type'] != $params['type']) {
                return true;
            }
            return '任务已经创建：'.$task->id;
        } else {
            return true;
        }
    }

    /**
     * 给予到MCP工具中的说明
     *
     * @return string
     */
    public function description(): string
    {
        return <<<EOF
            ```
            用户积分账户变动的监测
            command:$this->name
            params:
              - account:账户类型[in:score,balance],score:积分,balance:余额 
              - type:监测类型[in:in,out],in:收入,out:支出
            ```
        EOF;
    }

    /**
     * 任务详细描述
     *
     * @return string
     */
    public function getTaskRemark(array $params): string
    {
        $account = AccountType::ACCOUNT_TYPE_MAP[$params['account']] ?? '';
        $type    = match ($params['type']) {
            'in' => '收入',
            'out' => '支出',
            default => ''
        };
        return sprintf("%s:%s", $account, $type);
    }

    /**
     * 任务执行过程
     *
     * @return void
     */
    public function run(): void
    {
        try {
            #Todo 任务执行过程
            $params  = $this->task->params;
            $account = $params['account'];
            $type    = $params['type'];
            $startAt = $params['start_at'] ?? $this->task->created_at->toDateTimeString();
            if (! $this->task->user) {
                $this->task->delete();
                throw new Exception('用户不存在');
            }
            $model = AccountLog::where('account_id', $this->task->user->account->id)
                ->where('type', $account)
                ->where('created_at', '>', $startAt);
            list($model, $taskType) = match ($type) {
                'in' => [$model->where('amount', '>', 0), '收入'],
                'out' => [$model->where('amount', '<', 0), '消费'],
                default => [0, ''],
            };
            $chargeAmount             = abs($model->sum('amount'));
            $this->params['start_at'] = now()->toDateTimeString();
            $message                  = $chargeAmount > 0 ? sprintf("账户：%s,%s:%s。在[%s]到[%s]",
                $account,
                $taskType,
                $chargeAmount,
                $startAt,
                now()->toDateTimeString(),
            ) : '无异动';
            $this->success([
                'amount' => $chargeAmount,
            ], $message);
            if ($chargeAmount > 0) {
                $this->notification();
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->setLogs();
    }

}
