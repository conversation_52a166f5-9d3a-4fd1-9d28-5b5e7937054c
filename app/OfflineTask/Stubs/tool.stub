<?php

namespace App\OfflineTask\Tools;

use App\Models\User;
use App\Models\OfflineTask;
use App\Models\OfflineTaskLog;
use App\OfflineTask\BaseClass\BaseTask;
use Exception;

class {{ className }} extends BaseTask
{
    public string $name = '{{ taskName }}';
    public int       $minutes = 20;//运行间隔时间
    protected string $cnName  = '账户变动监测';//任务中文名称

    /**
     * 是否可以添加任务：主要用作去重
     *
     * @param  \App\Models\User  $user
     * @param  array  $params
     * @return string|true
     */
    public function canJoin(User $user, array $params)
    {
        $task = OfflineTask::ofUser($user)
            ->where('status', OfflineTask::STATUS_ING)
            ->where('command', $this->name)
            ->first();
        if ($task) {
            if ($task->params['account'] != $params['account']) {
                return true;
            }
            if ($task->params['type'] != $params['type']) {
                return true;
            }
            return '任务已经创建：'.$task->id;
        } else {
            return true;
        }
    }

    /**
     * 给予到MCP工具中的说明
     *
     * @return string
     */
    public function description(): string
    {
        return <<<EOF
            ```
            用户积分账户变动的监测
            command:$this->name
            params:
              - account:账户类型[in:score,balance],score:积分,balance:余额
              - type:监测类型[in:in,out],in:收入,out:支出
            ```
        EOF;
    }

    /**
     * 任务详细描述，创建任务是写入remark再列表中展示
     *
     * @return string
     */
    public function getTaskRemark(array $params): string
    {
        return '积分消费';
    }

    /**
     * 任务执行过程
     *
     * @return void
     */
    public function run(): void
    {
        try {
            #Todo 任务执行过程
            $this->success([
                'abc' => 'abc'
            ],'任务执行\通知结果');//记录成功数据
            if (true) {
                $this->notification();//执行通知
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->setLogs();//产生记录
    }

}
