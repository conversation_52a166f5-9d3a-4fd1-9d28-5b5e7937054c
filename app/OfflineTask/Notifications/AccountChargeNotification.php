<?php

namespace App\OfflineTask\Notifications;

use Illuminate\Database\Eloquent\Model;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\DatabaseMessage;
use Modules\Notification\Messages\IMMessage;

class AccountChargeNotification extends BaseNotification
{
    /**
     * 固定传入
     *
     * @param  array  $params  任务执行时入参
     * @param  array  $result  任务完成后出参数
     */
    public function __construct(protected array $params, protected array $result)
    {
    }

    public function toDatabase(Model $notifiable): DatabaseMessage|array
    {
        return [
            'title'   => self::getTitle(),
            'content' => $this->result['message'],
        ];
    }

    public static function getTitle(): string
    {
        return '账户变动通知';
    }

    public function toIM(Model $notifiable): IMMessage|array
    {
        $imUser = $notifiable->imUser;
        if (! $imUser) {
            return [];
        }
        return new IMMessage(
            static::getTitle(),
            $this->result['message'],
            [$imUser->im_user_id]
        );
    }
}