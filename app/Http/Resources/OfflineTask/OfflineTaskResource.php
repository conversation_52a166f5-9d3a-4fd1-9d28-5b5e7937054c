<?php

namespace App\Http\Resources\OfflineTask;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OfflineTaskResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'           => $this->id,
            'command'      => $this->command,
            'name'         => $this->name,
            'remark'       => $this->remark,
            'status'       => [
                'code' => $this->status,
                'text' => $this->status_text,
            ],
            'minutes'      => $this->minutes,
            'next_at'      => $this->next_at?->toDateTimeString() ?: '',
            'last_at'      => $this->last_at?->toDateTimeString() ?: '',
            'last_message' => $this->lastLog?->result['message'] ?: '',
        ];
    }
}