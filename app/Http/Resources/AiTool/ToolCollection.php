<?php

namespace App\Http\Resources\AiTool;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class ToolCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return new ToolResource($item);
            }),
        ], $this->page());
    }
}