<?php

namespace App\Http\Resources\Account;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class LogsCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return new LogResoucre($item);
            }),
        ], $this->page());
    }
}