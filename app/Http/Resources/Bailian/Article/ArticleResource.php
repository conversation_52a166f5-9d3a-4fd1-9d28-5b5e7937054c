<?php

namespace App\Http\Resources\Bailian\Article;

use App\Http\Resources\Bailian\Knowledge\KnowledgeItemResource;
use App\Http\Resources\Bailian\Tag\TagResource;
use App\Http\Resources\InteractionResourceTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticleResource extends JsonResource
{
    use InteractionResourceTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                  => $this->id,
            'title'               => $this->title,
            'content'             => $this->content,
            'type'                => [
                'value' => $this->type->value,
                'text'  => $this->type->toString(),
            ],
            'parent_id'           => $this->parent_id,
            'parent'              => $this->when($this->parent_id, function () {
                return new ArticleBaseResource($this->parent);
            }),
            'children'            => ChildrenResource::collection($this->children),
            'tags'                => TagResource::collection($this->tags),
            'count'               => [
                'browse_count'   => $this->browse_count ?? 0,
                'children_count' => $this->children()->count() ?? 0,
                'word_count'     => $this->getWordCount(),
            ],
            'knowledge_item'      => $this->knowledgeItem ? new KnowledgeItemResource($this->knowledgeItem) : null,
            'can'                 => $this->getCan($request->kernel->user()),
            'is_in_knowledge'     => $this->isInKnowledge(),
            'interaction'         => $this->getInteraction(),
            'meeting_info_status' => $this->getMeetingSummaryStatus(),
            'created_at'          => (string) $this->created_at,
            'updated_at'          => (string) $this->updated_at,
        ];
    }
}
