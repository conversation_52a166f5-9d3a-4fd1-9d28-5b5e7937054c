<?php

namespace App\Http\Controllers\Api\AiChat;

use App\Http\Controllers\Controller;
use App\Http\Resources\OfflineTask\OfflineTaskListCollection;
use App\Models\OfflineTask;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class OfflineTaskController extends Controller
{
    public function lists(Request $request)
    {
        $user   = $request->kernel->user();
        $status = $request->status ?: '';
        $lists  = OfflineTask::ofUser($user)
            ->with(['lastLog'])
            ->when($status, function (Builder $builder, $status) {
                $builder->where('status', $status);
            })
            ->orderBy('id', 'desc')
            ->paginate($request->page_size ?: 20);
        return $request->kernel->success(new OfflineTaskListCollection($lists));
    }

    public function stop(Request $request)
    {
        $user = $request->kernel->user();
        $task = OfflineTask::ofUser($user)->where('id', $request->id)->first();
        if ($task) {
            if ($task->canStop()) {
                $task->doStop();
                return $request->kernel->success([], '任务暂停成功');
            } else {
                return $request->kernel->error('任务不可暂停');
            }
        } else {
            return $request->kernel->error('任务不存在');
        }
    }

    public function continue(Request $request)
    {
        $user = $request->kernel->user();
        $task = OfflineTask::ofUser($user)->where('id', $request->id)->first();
        if ($task) {
            if ($task->canContinue()) {
                $task->doContinue();
                return $request->kernel->success([], '任务恢复执行');
            } else {
                return $request->kernel->error('任务不可恢复执行');
            }
        } else {
            return $request->kernel->error('任务不存在');
        }
    }

    public function delete(Request $request)
    {
        $user = $request->kernel->user();
        $task = OfflineTask::ofUser($user)->where('id', $request->id)->first();
        if ($task) {
            $task->delete();
            return $request->kernel->success([], '任务删除成功');
        } else {
            return $request->kernel->error('任务不存在');
        }
    }
}