<?php

namespace App\Http\Controllers\Api\Prompts;

use Exception;
use Illuminate\Http\Request;

class RtcController extends BasePromptController
{
    public function role(Request $request)
    {
        try {
            $prompt = $request->prompt ?? '随机生成一个';
            $system = wateGetSystemPrompt('RtcCreateRole');
            return $request->kernel->success([
                'content' => $this->doChat($system, $prompt),
            ]);
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }
}