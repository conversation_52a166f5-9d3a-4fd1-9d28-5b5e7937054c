<?php

namespace App\Http\Controllers\Api\Vidu;

use App\Http\Controllers\Controller;
use App\Http\Requests\Vidu\AudioDrawRequest;
use App\Http\Resources\Vidu\ViduAudioDrawCollection;
use App\Http\Resources\Vidu\ViduAudioDrawResource;
use App\Models\PluginViduAudioDraw;
use DB;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AudioController extends Controller
{
    /**
     * 获取可控音效任务列表
     */
    public function index(Request $request)
    {
        $user = $request->kernel->user();

        $list = PluginViduAudioDraw::query()
            ->where('user_id', $user->id)
            ->latest('id')
            ->paginate($request->per_page ?? 10);

        return $request->kernel->success(new ViduAudioDrawCollection($list));
    }

    /**
     * 查询任务状态
     */
    public function query(Request $request)
    {
        $no = $request->no;
        $user = $request->kernel->user();
        
        try {
            $audioDraw = PluginViduAudioDraw::where('no', $no)->first();
            if (!$audioDraw) {
                return $request->kernel->error('任务不存在');
            }

            // 检查任务是否属于当前用户
            if ($audioDraw->user_id != $user->id) {
                return $request->kernel->error('无权访问此任务');
            }

            if ($audioDraw->canPolling()) {
                $audioDraw->audioQuery();
            }

            return $request->kernel->success(new ViduAudioDrawResource($audioDraw));
        } catch (Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * 创建可控音效任务
     */
    public function draw(AudioDrawRequest $request)
    {
        try {
            DB::beginTransaction();
            
            $model = $request->model ?? 'audio1.0';
            $duration = $request->duration ?? 10;
            $timingPrompts = $request->timing_prompts;
            $seed = $request->seed;
            
            $user = $request->kernel->user();

            $data = [
                'user_id' => $user->id,
                'model' => $model,
                'duration' => $duration,
                'timing_prompts' => $timingPrompts,
                'seed' => $seed,
                'status' => PluginViduAudioDraw::STATUS_INIT,
                'is_asset' => $request->is_asset ? true : false,
            ];

            // 创建任务
            $audioDraw = PluginViduAudioDraw::create($data);
            
            // 立即开始处理
            $audioDraw->draw();
            
            DB::commit();
            return $request->kernel->success(new ViduAudioDrawResource($audioDraw), '已排入制作队列');
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Vidu可控音效任务创建失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'params' => $request->all(),
            ]);
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * 删除任务
     */
    public function destroy(Request $request)
    {
        $no = $request->no;
        $user = $request->kernel->user();
        
        try {
            $audioDraw = PluginViduAudioDraw::where('no', $no)->first();
            if (!$audioDraw) {
                return $request->kernel->error('任务不存在');
            }

            // 检查任务是否属于当前用户
            if ($audioDraw->user_id != $user->id) {
                return $request->kernel->error('无权访问此任务');
            }

            // 只能删除已完成或失败的任务
            if (!in_array($audioDraw->status, [
                PluginViduAudioDraw::STATUS_SUCCESS,
                PluginViduAudioDraw::STATUS_ERROR
            ])) {
                return $request->kernel->error('只能删除已完成或失败的任务');
            }

            $audioDraw->delete();
            return $request->kernel->success([], '删除成功');
        } catch (Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }
}
