<?php

namespace App\Http\Controllers\Api\FastGpt;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Http\Requests\FastGpt\App\AddAppRequest;
use App\Http\Requests\FastGpt\App\UpdateAppRequest;
use App\Http\Resources\FastGpt\AppCollection;
use App\Http\Resources\FastGpt\AppResource;
use App\Http\Resources\FastGpt\KnowledgeBaseResource;
use App\Models\Enums\FastgptAppLevelEnum;
use App\Models\FastgptApp;
use App\Models\FastgptKnowledge;
use App\Packages\FastGpt\FastGpt;
use App\Traits\FastGptTrait;
use Exception;
use Illuminate\Http\Request;

class AppController extends ApiController
{
    use FastGptTrait;

    public function list(Request $request)
    {
        $user   = $request->kernel->user();
        $status = $request->status ?? '';

        $apps = FastgptApp::query()
            ->when(is_numeric($status), function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->where('user_id', $user->id)
            ->paginate();

        return $request->kernel->success(new AppCollection($apps));
    }

    /**
     * Notes: 公开列表
     *
     * @Author: 玄尘
     * @Date: 2025/3/28 13:01
     */
    public function public(Request $request)
    {
        $name = $request->name ?? '';
        $apps = FastgptApp::query()
            ->where('level', FastgptAppLevelEnum::ALL->value)
            ->when($name, function ($query) use ($name) {
                $query->where('name', 'like', "%{$name}%");
            })
            ->OfEnabled()
            ->paginate();

        return $request->kernel->success(new AppCollection($apps));
    }

    /**
     * Notes: 创建
     *
     * @Author: 玄尘
     * @Date: 2024/12/17 12:58
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function create(Request $request)
    {
        $addAppRequest = new AddAppRequest();
        $request->kernel->validate($addAppRequest->rules(), $addAppRequest->messages());

        $user          = $request->kernel->user();
        $name          = $request->input('name');
        $avatar        = $request->input('avatar');
        $welcome       = $request->input('welcome');
        $prologue      = $request->input('prologue');
        $dataset_ids   = $request->input('dataset_ids');
        $level         = $request->input('level', 'all');
        $department_id = $request->input('department_id', 0);
        $company_id    = $request->input('company_id', 0);

        $templateChat = config('fastgpt.chat.simple');
        $pushData     = config('fastgpt.chat.pushData');

        $templateChat['name'] = $name;
        if ($avatar) {
            $templateChat['avatar'] = $avatar;
        }

        if (! $welcome) {
            $welcome = config('fastgpt.default_welcome', '');
        }
        if (isset($pushData['versionName'])) {
            $pushData['versionName'] = now()->format('Y-m-d H:i:s');
        }

        //提示词
        $pushData = $this->resetChatTemplate($pushData, 'SYSTEMPROMPT_TEXT', $welcome);

        //开场白
        if ($prologue) {
            $pushData = $this->resetChatTemplate($pushData, 'WELCOME_TEXT', $prologue);
        }

        $dataset_ids = $this->checkDatasetIds($dataset_ids);
        //关联知识库
        if ($dataset_ids) {
            $pushData = $this->prepareDatasetValues($pushData, $dataset_ids);
        }
        $result = FastGpt::app()->appCreate($templateChat);
        $app_id = $result['data'];
        if (! empty($pushData)) {
            $result = FastGpt::app()->appPublish($app_id, $pushData);
        }
        $result   = FastGpt::app()->appCreateToken($app_id);
        $apiToken = $result['data'];

        $app = FastgptApp::updateOrCreate([
            'user_id'    => $user->id,
            'app_id'     => $app_id,
            'company_id' => $company_id,
        ], [
            'name'          => $name,
            'permission'    => 'private',
            'image'         => $avatar ?? '',
            'welcome'       => $welcome ?? '',
            'dataset_ids'   => explode(',', $dataset_ids),
            'model'         => 'qwen-plus',
            'prologue'      => $prologue ?? '',
            'level'         => $level,
            'department_id' => $department_id,
            'api_token'     => $apiToken,
        ]);

        return $request->kernel->success('创建成功');
    }

    public function update(Request $request)
    {
        $updateAppRequest = new UpdateAppRequest();

        $request->kernel->validate($updateAppRequest->rules(), $updateAppRequest->messages());

        $user        = $request->kernel->user();
        $app_id      = $request->input('app_id');
        $permission  = $request->input('permission', 'private');
        $description = $request->input('description');
        $avatar      = $request->input('avatar');
        $name        = $request->input('name');
        $level       = $request->input('level');
        $dataset_ids = $request->input('dataset_ids', '');
        $welcome     = $request->input('welcome');
        $prologue    = $request->input('prologue');
        $app         = $this->getAppById($app_id);
        if (! $app->canEdit($user)) {
            throw new ValidatorException('您没有权限');
        }

        $pushData = config('fastgpt.chat.pushData');

        $welcome  = $welcome ?? $app->welcome;
        $prologue = $prologue ?? $app->prologue;

        $dataset_ids = $this->checkDatasetIds($dataset_ids);

        $datasetValues = $this->formatDatasetIds($dataset_ids);

        $pushData = $this->updateDatasetNodes($pushData, $datasetValues);

        //提示词
        if ($welcome) {
            $pushData = $this->resetChatTemplate($pushData, 'SYSTEMPROMPT_TEXT', $welcome);
        }

        //开场白
        if ($prologue) {
            $pushData = $this->resetChatTemplate($pushData, 'WELCOME_TEXT', $prologue);
        }

        $result = FastGpt::app()->appUpdate($app_id, $name, $permission, $description);
        if (! empty($pushData)) {
            $result = FastGpt::app()->appPublish($app_id, $pushData);
        }

        $app->permission = $permission;
        $app->prologue   = $prologue;
        $app->welcome    = $welcome;
        if ($description) {
            $app->description = $description;
        }
        if ($name) {
            $app->name = $name;
        }
        if ($level) {
            $app->level = $level;
        }
        if ($avatar) {
            $app->image = $avatar;
        }
        if ($dataset_ids) {
            $app->dataset_ids = $this->str2Arr($dataset_ids);
        }

        $app->save();
        return $request->kernel->success('更新成功');
    }

    public function status(Request $request)
    {
        $request->kernel->validate([
            'app_id' => 'required',
        ], [
            'app_id.required' => '缺少应用id',
        ]);

        $user   = $request->kernel->user();
        $app_id = $request->input('app_id');

        $app = $this->getAppById($app_id);
        if (! $app->canStatus($user)) {
            throw new ValidatorException('您没有权限');
        }

        $app->status = $app->status ? 0 : 1;
        $app->save();

        return $request->kernel->success('操作成功');
    }

    public function level(Request $request)
    {
        $request->kernel->validate([
            'app_id' => 'required',
            'level'  => 'required|in:all,user',
        ], [
            'app_id.required' => '缺少应用id',
            'level.required'  => '缺少权限',
            'level.in'        => '缺少参数不对',
        ]);

        $user   = $request->kernel->user();
        $app_id = $request->input('app_id');
        $level  = $request->input('level');

        $app = $this->getAppById($app_id);
        if (! $app->canStatus($user)) {
            throw new ValidatorException('您没有权限');
        }

        $app->level = $level;
        $app->save();

        return $request->kernel->success([
            'level' => $level
        ]);
    }

    public function delete(Request $request)
    {
        try {
            \DB::beginTransaction();
            $request->kernel->validate([
                'app_id' => 'required',
            ], [
                'app_id.required' => '缺少应用id',
            ]);

            $user = $request->kernel->user();

            $app_id = $request->input('app_id');
            $app    = $this->getAppById($app_id);
            if (! $app->canDelete($user)) {
                throw new ValidatorException('您没有权限');
            }
            $res = FastGpt::app()->appDelete($app_id);
            $app->chats()->delete();
            $app->delete();
            \DB::commit();
            return $request->kernel->success('删除成功');
        } catch (Exception $exception) {
            \DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 可用的知识库列表
     *
     * @Author: 玄尘
     * @Date: 2024/12/30 10:01
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function knowledges(Request $request)
    {
        $userId = $request->kernel->id();

        $knowledge = FastgptKnowledge::query()
            ->withCount('knowledgeSets')
            ->where('user_id', $userId)
            ->OfEnabled()
            ->get();

        return $request->kernel->success(KnowledgeBaseResource::collection($knowledge));
    }

    public function show(Request $request)
    {
        $request->kernel->validate([
            'app_id' => 'required',
        ], [
            'app_id.required' => '缺少应用id',
        ]);

        $app = $this->getAppById($request->input('app_id'));

        return $request->kernel->success(new AppResource($app));
    }

}
