<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Packages\WateCheckPhone;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Modules\User\Rules\MobileRule;
use Tinywan\Jwt\JwtToken;

class IndexController extends Controller
{
    public function getCode(Request $request)
    {
        $request->kernel->validate([
            'phone' => [
                'required',
                new MobileRule()
            ],
            'type'  => 'required',
        ], [
            'phone.required' => '请输入手机号',
            'type.required'  => '渠道参数错误'
        ]);
        WateCheckPhone::send($request->phone, $request->type);
        return $request->kernel->success([]);
    }

    public function loginMobile(Request $request)
    {
        $request->kernel->validate([
            'phone' => [
                'required',
                new MobileRule()
            ],
            'type'  => 'required',
            'code'  => 'required',
        ], [
            'phone.required' => '请输入手机号',
            'type.required'  => '渠道参数错误',
            'code.required'  => '请输入验证码'
        ]);
        $mobile = $request->phone;
        $type   = $request->type;
        $code   = $request->code;
        WateCheckPhone::checkPhone($mobile, $type, $code);
        $user    = User::where('username', $mobile)->first();
        $isFirst = false;
        if (! $user) {
            $user    = User::create([
                'username' => $mobile,
            ]);
            $isFirst = true;
        }
        $key                = md5(Str::random(32));
        $token              = JwtToken::generateToken([
            'id'  => $user->id,
            'key' => $key,
        ]);
        $token['key']       = $key;
        $token['user_info'] = [
            'uid'      => $user->id,
            'nickname' => $user->info->nickname,
            'phone'    => $user->username,
            'is_first' => $isFirst ? 1 : 0,
        ];
        return $request->kernel->success($token);
    }
}