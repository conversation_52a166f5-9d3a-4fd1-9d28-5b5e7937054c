<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class OfflineTask extends Model
{
    use BelongsToUser, HasUuids;

    const STATUS_INIT = 0;
    const STATUS_ING  = 1;
    const STATUS_OVER = 2;
    const STATUS_STOP = 9;

    const STATUS = [
        self::STATUS_INIT => '初始化',
        self::STATUS_ING  => '执行中',
        self::STATUS_OVER => '结束',
        self::STATUS_STOP => '暂停',
    ];

    public    $incrementing = false;
    protected $keyType      = 'string';

    protected $casts = [
        'params'  => 'json',
        'next_at' => 'datetime',
        'last_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        self::deleted(function ($model) {
            OfflineTaskLog::where('task_id', $model->id)->delete();
        });
    }

    public function getStatusTextAttribute(): string
    {
        return self::STATUS[$this->status] ?? '';
    }

    public function logs(): HasMany
    {
        return $this->hasMany(OfflineTaskLog::class, 'task_id', 'id');
    }

    public function lastLog(): HasOne
    {
        return $this->hasOne(OfflineTaskLog::class, 'task_id', 'id')
            ->orderByDesc('created_at')
            ->orderByDesc('id');
    }

    public function doStop()
    {
        $this->status = self::STATUS_STOP;
        $this->save();
    }

    public function doContinue()
    {
        $this->status = self::STATUS_ING;
        $this->save();
    }

    public function cans(): array
    {
        return [
            'delete'   => true,
            'stop'     => $this->canStop(),
            'continue' => $this->canContinue(),
        ];
    }

    public function canStop(): bool
    {
        return in_array($this->status, [
            self::STATUS_ING,
        ]);
    }

    public function canContinue(): bool
    {
        return in_array($this->status, [
            self::STATUS_ING,
        ]);
    }
}
