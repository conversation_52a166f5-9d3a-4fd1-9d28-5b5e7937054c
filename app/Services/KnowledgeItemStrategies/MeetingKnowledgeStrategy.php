<?php

namespace App\Services\KnowledgeItemStrategies;

use App\Enums\Bailian\ArticleProcessingStatusEnum;
use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Enums\Bailian\ItemSyncStatusEnum;
use App\Events\KnowledgeItemCreatedEvent;
use App\Jobs\BaiLian\SyncKnowledgeItemToAlibabaJob;
use App\Models\BailianArticle;
use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeItem;
use App\Services\KnowledgeTool;
use Exception;
use Modules\Storage\Models\Upload;

/**
 * 会议笔记知识库策略
 * 
 * 专门处理会议笔记的知识库操作，与普通文章策略的主要区别：
 * 1. 只有在会议总结完成后才能加入知识库
 * 2. 使用处理后的会议总结内容而不是原始录音数据
 */
class MeetingKnowledgeStrategy extends AbstractKnowledgeItemStrategy
{
    /**
     * 同步添加会议笔记到阿里云知识库
     */
    public function addToKnowledgeBase($item, BailianKnowledge $knowledge, string $categoryId, array $options): void
    {
        // 检查会议总结是否已完成
        if (!$this->isMeetingSummaryCompleted($item)) {
            throw new Exception('会议总结尚未完成，无法加入知识库');
        }

        // 确保存储文件存在
        $storage = $this->ensureStorageExists($item);

        // 获取知识库工具
        $knowledgeTool = app(KnowledgeTool::class);

        // 添加文档到知识库
        $documentInfo = $knowledgeTool->addDocumentToKnowledge(
            $knowledge,
            $storage->path_url,
            $categoryId,
            $storage->hash,
            $storage->size,
            $knowledge->workspace_id
        );

        // 创建或更新知识库文件记录
        $this->saveKnowledgeFile($item, $knowledge, $storage, $categoryId, $documentInfo);

        // 创建知识库项目记录并设置为已同步状态
        $knowledgeItem = $this->createKnowledgeItem($item, $knowledge, $options);
        $knowledgeItem->update([
            'sync_status' => ItemSyncStatusEnum::SYNCED,
            'sync_error'  => null,
            'synced_at'   => now(),
        ]);
    }

    /**
     * 添加会议笔记到知识库（快速流程）
     */
    public function addToKnowledge(
        $item,
        BailianKnowledge $knowledge,
        string $categoryId,
        array $options
    ): BailianKnowledgeItem {
        // 检查会议总结是否已完成
        if (!$this->isMeetingSummaryCompleted($item)) {
            // 如果会议总结未完成，创建本地记录但标记为等待状态
            $knowledgeItem = $this->createKnowledgeItem($item, $knowledge, $options);
            $knowledgeItem->update([
                'sync_status' => ItemSyncStatusEnum::PENDING,
                'sync_error'  => '等待会议总结完成',
            ]);
            
            $this->logger->info('会议笔记总结未完成，创建待同步记录', [
                'article_id' => $item->id,
                'knowledge_item_id' => $knowledgeItem->id
            ]);
            
            return $knowledgeItem;
        }

        // 会议总结已完成，正常处理
        $knowledgeItem = $this->createKnowledgeItem($item, $knowledge, $options);

        // 触发知识库项目创建事件，异步处理同步
        event(new KnowledgeItemCreatedEvent($knowledgeItem));

        return $knowledgeItem;
    }

    /**
     * 同步会议笔记到阿里云知识库
     */
    public function syncToAlibaba($item, BailianKnowledge $knowledge, string $categoryId): void
    {
        // 检查会议总结是否已完成
        if (!$this->isMeetingSummaryCompleted($item)) {
            throw new Exception('会议总结尚未完成，无法同步到阿里云');
        }

        // 获取旧文件信息（用于计算大小变化和删除旧文件）
        $oldSize       = 0;
        $oldStorage    = null;
        $knowledgeFile = $item->knowledgeFile;
        if ($knowledgeFile && $knowledgeFile->storage_id) {
            $oldStorage = Upload::find($knowledgeFile->storage_id);
            $oldSize    = $oldStorage ? $oldStorage->size : 0;
        }

        // 确保存储文件存在
        $storage = $this->ensureStorageExists($item);

        // 获取知识库工具
        $knowledgeTool = app(KnowledgeTool::class);

        // 添加文档到知识库
        $documentInfo = $knowledgeTool->addDocumentToKnowledge(
            $knowledge,
            $storage->path_url,
            $categoryId,
            $storage->hash,
            $storage->size,
            $knowledge->workspace_id
        );

        // 创建或更新知识库文件记录
        $this->saveKnowledgeFile($item, $knowledge, $storage, $categoryId, $documentInfo);

        // 计算大小变化并更新知识库大小
        $sizeChange = $storage->size - $oldSize;
        if ($sizeChange != 0) {
            $this->updateKnowledgeSize($knowledge, $sizeChange);

            $this->logger->info('会议笔记同步完成，更新知识库大小', [
                'article_id'   => $item->id,
                'knowledge_id' => $knowledge->id,
                'old_size'     => $oldSize,
                'new_size'     => $storage->size,
                'size_change'  => $sizeChange
            ]);
        }

        // 删除旧文件（如果存在且不同于新文件）
        if ($oldStorage && $oldStorage->id !== $storage->id) {
            $this->deleteOldKnowledgeFile($oldStorage, $knowledge);
        }
    }

    /**
     * 从知识库中移除会议笔记
     */
    public function removeFromKnowledge($item): void
    {
        // 处理知识库文件记录
        $knowledge = $this->handleKnowledgeFileRemoval($item);

        // 处理知识库项目记录
        if (! $knowledge) {
            $knowledge = $this->handleKnowledgeItemRemoval($item);
        } else {
            $this->handleKnowledgeItemRemoval($item);
        }
    }

    /**
     * 会议笔记内容更新后的同步处理
     */
    public function handleContentUpdate($item): void
    {
        $knowledgeItem = $item->knowledgeItem;
        if (! $knowledgeItem) {
            return; // 不在知识库中，无需处理
        }

        // 检查会议总结是否已完成
        if (!$this->isMeetingSummaryCompleted($item)) {
            // 会议总结未完成，标记为等待状态
            $knowledgeItem->update([
                'sync_status' => ItemSyncStatusEnum::PENDING,
                'sync_error'  => '等待会议总结完成',
            ]);
            
            $this->logger->info('会议笔记总结未完成，标记为等待同步', [
                'article_id'        => $item->id,
                'knowledge_item_id' => $knowledgeItem->id
            ]);
            return;
        }

        // 如果正在同步中，不重复触发
        if ($knowledgeItem->isSyncing()) {
            $this->logger->info('会议笔记正在同步中，跳过内容更新同步', [
                'article_id'        => $item->id,
                'knowledge_item_id' => $knowledgeItem->id
            ]);
            return;
        }

        // 重置 storage_id，旧文件将在 syncToAlibaba 中删除
        $item->update([
            'storage_id' => null
        ]);

        // 重置同步状态为待同步
        $knowledgeItem->update([
            'sync_status' => ItemSyncStatusEnum::PENDING,
            'sync_error'  => null,
        ]);

        // 重新触发异步同步
        SyncKnowledgeItemToAlibabaJob::dispatch($knowledgeItem);

        $this->logger->info('会议笔记内容更新，重新触发同步', [
            'article_id'        => $item->id,
            'knowledge_item_id' => $knowledgeItem->id
        ]);
    }

    /**
     * 检查会议总结是否已完成
     */
    private function isMeetingSummaryCompleted(BailianArticle $item): bool
    {
        // 检查文章类型
        if ($item->type !== BailianArticleTypeEnum::MEETING) {
            return true; // 非会议笔记，直接返回true
        }

        // 检查处理状态
        return $item->processing_status === ArticleProcessingStatusEnum::COMPLETED;
    }

    /**
     * 确保会议笔记存储文件存在
     */
    private function ensureStorageExists($item): Upload
    {
        if ($item->storage_id && ! $item->storage) {
            $this->logger->warning('会议笔记storage_id存在但storage记录不存在，重新生成', [
                'article_id' => $item->id,
                'storage_id' => $item->storage_id
            ]);
            $item->update(['storage_id' => null]);
        }

        // 如果没有storage_id或storage记录不存在，生成新的存储文件
        if (! $item->storage_id || ! $item->storage) {
            $item->prepareStorage();
            $item = $item->refresh();
        }

        // 获取存储信息
        $storage = $item->storage;
        if (! $storage) {
            throw new Exception('会议笔记存储文件创建失败');
        }

        return $storage;
    }

    public function getSupportedModelClass(): string
    {
        return BailianArticle::class;
    }
}
