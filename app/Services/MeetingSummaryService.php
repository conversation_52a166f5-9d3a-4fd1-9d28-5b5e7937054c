<?php

namespace App\Services;

use App\Enums\Bailian\ArticleProcessingStatusEnum;
use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Events\KnowledgeItemCreatedEvent;
use App\Jobs\BaiLian\GenerateArticleTagsJob;
use App\Models\BailianArticle;
use App\Packages\XfyunCkm\XfyunCkm;
use App\Packages\XfyunCkm\XfyunCkmException;
use Exception;
use Log;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\StreamedResponse;

/**
 * 会议录音处理服务
 * 专门处理会议录音笔记的讯飞CKM集成功能
 */
class MeetingSummaryService
{
    public LoggerInterface $logger;

    public function __construct()
    {
        $this->logger = Log::channel('bailian');
    }

    /**
     * 检查是否需要生成会议总结
     */
    public function needsGenerateSummary(BailianArticle $article): bool
    {
        $content = $article->content;
        if (is_string($content)) {
            $content = json_decode($content, true);
        }

        // 如果没有data字段或data为空，且有text_record，则需要生成总结
        return empty($content['data']) && ! empty($content['text_record']);
    }

    /**
     * 提交会议总结任务到讯飞CKM
     * 立即返回任务ID，不等待结果
     */
    public function submitMeetingSummaryTask(BailianArticle $article): array
    {
        // 检查是否为会议录音类型
        if ($article->type !== BailianArticleTypeEnum::MEETING) {
            return [
                'success' => false,
                'message' => '非会议录音类型笔记'
            ];
        }

        // 检查是否需要处理
        if (! $this->needsGenerateSummary($article)) {
            return [
                'success' => false,
                'message' => '该笔记不需要生成总结'
            ];
        }

        try {
            // 解析content数据
            $content = $article->content;
            if (is_string($content)) {
                $content = json_decode($content, true);
            }

            // 检查转录数据
            if (! isset($content['text_record']) || empty($content['text_record'])) {
                return [
                    'success' => false,
                    'message' => '笔记没有转录数据'
                ];
            }

            // 解析转录数据
            $textRecord = $content['text_record'];
            if (is_string($textRecord)) {
                $textRecord = json_decode($textRecord, true);
            }

            if (! is_array($textRecord) || empty($textRecord)) {
                return [
                    'success' => false,
                    'message' => '转录数据格式错误'
                ];
            }

            // 转换为会议段落格式
            $paragraphs = $this->convertTextRecordToParagraphs($textRecord);

            if (empty($paragraphs)) {
                return [
                    'success' => false,
                    'message' => '转录数据为空'
                ];
            }

            $this->logger->info('提交会议总结任务到讯飞CKM', [
                'article_id'        => $article->id,
                'paragraphs_count'  => count($paragraphs),
                'total_text_length' => array_sum(array_map(fn($p) => mb_strlen($p['ws'] ?? ''), $paragraphs))
            ]);

            $callbackUrl = 'https://ai.api.watestar.com/api/callback/xfyun_ckm';

            // 调用讯飞CKM服务提交任务
            $result = XfyunCkm::aggregate()->requestComplete($paragraphs, $callbackUrl);
            $taskId = $result['taskId'];

            // 保存任务ID到数据库
            $article->update([
                'xfyun_task_id'         => $taskId,
                'processing_status'     => ArticleProcessingStatusEnum::PROCESSING,
                'processing_started_at' => now(),
                'processing_error'      => null
            ]);

            $this->logger->info('会议总结任务提交成功', [
                'article_id' => $article->id,
                'task_id'    => $taskId
            ]);

            return [
                'success'    => true,
                'message'    => '会议总结任务已提交',
                'article_id' => $article->id,
                'task_id'    => $taskId
            ];
        } catch (XfyunCkmException $e) {
            $this->handleXfyunCkmException($e, $article->id);
            return [
                'success' => false,
                'message' => '提交任务失败: '.$e->getMessage()
            ];
        } catch (Exception $e) {
            $this->logger->error('提交会议总结任务异常', [
                'article_id' => $article->id,
                'error'      => $e->getMessage()
            ]);
            return [
                'success' => false,
                'message' => '提交任务失败: '.$e->getMessage()
            ];
        }
    }

    /**
     * 查询讯飞CKM任务状态
     * 直接查询讯飞API，如果完成则保存结果
     */
    public function queryTaskStatus(BailianArticle $article): array
    {
        if (! $article->xfyun_task_id) {
            return [
                'success' => false,
                'message' => '该笔记没有关联的处理任务'
            ];
        }

        try {
            $this->logger->info('查询讯飞CKM任务状态', [
                'article_id' => $article->id,
                'task_id'    => $article->xfyun_task_id
            ]);

            // 直接查询讯飞API
            $result = XfyunCkm::query()->getQueryResult($article->xfyun_task_id);

            $response = [
                'success'      => true,
                'article_id'   => $article->id,
                'task_id'      => $article->xfyun_task_id,
                'is_completed' => $result['isCompleted'],
                'is_success'   => $result['isSuccess'],
                'is_failed'    => $result['isFailed'],
                'task_status'  => $result['taskStatus']
            ];

            // 如果任务完成，保存结果
            if ($result['isCompleted']) {
                if ($result['isSuccess']) {
                    // 保存总结到笔记（saveMeetingSummary内部会更新状态和触发知识库同步）
                    $this->saveMeetingSummary($article, $result);

                    $response['message']     = '会议总结生成完成';
                    $response['has_content'] = true;

                    $this->logger->info('会议总结处理完成并已保存', [
                        'article_id' => $article->id,
                        'task_id'    => $article->xfyun_task_id
                    ]);
                } else {
                    // 任务失败
                    $article->update([
                        'processing_status'       => ArticleProcessingStatusEnum::FAILED,
                        'processing_completed_at' => now(),
                        'processing_error'        => '讯飞CKM处理失败'
                    ]);

                    $response['message']     = '会议总结生成失败';
                    $response['has_content'] = false;
                }
            } else {
                $response['message']     = '任务处理中，请稍后查询';
                $response['has_content'] = false;
            }

            return $response;
        } catch (XfyunCkmException $e) {
            $this->handleXfyunCkmException($e, $article->id);
            return [
                'success' => false,
                'message' => '查询任务状态失败: '.$e->getMessage()
            ];
        } catch (Exception $e) {
            $this->logger->error('查询任务状态异常', [
                'article_id' => $article->id,
                'task_id'    => $article->xfyun_task_id,
                'error'      => $e->getMessage()
            ]);
            return [
                'success' => false,
                'message' => '查询任务状态失败: '.$e->getMessage()
            ];
        }
    }

    /**
     * 获取文章的处理状态信息
     */
    public function getArticleStatus(BailianArticle $article): array
    {
        return [
            'article_id'        => $article->id,
            'has_task'          => ! empty($article->xfyun_task_id),
            'task_id'           => $article->xfyun_task_id,
            'processing_status' => $article->processing_status->value,
            'status_text'       => $article->processing_status->toString(),
            'error'             => $article->processing_error,
            'started_at'        => (string) $article->processing_started_at,
            'completed_at'      => (string) $article->processing_completed_at,
            'can_reprocess'     => $article->canReprocessMeetingSummary(),
            'needs_summary'     => $this->needsGenerateSummary($article)
        ];
    }

    /**
     * 流式返回会议总结内容
     *
     * @param  BailianArticle  $article
     * @return StreamedResponse
     */
    public function streamMeetingSummary(BailianArticle $article): StreamedResponse
    {
        return new StreamedResponse(function () use ($article) {
            // 设置流式响应头
            ignore_user_abort(true);
            set_time_limit(300); // 5分钟超时
            session_write_close();
            header('Content-Type: text/event-stream');
            header('Cache-Control: no-cache');
            header('Connection: keep-alive');
            header('X-Accel-Buffering: no');
            ob_implicit_flush(1);

            try {
                // 发送开始事件
                $this->sendSSEEvent('start', '开始获取会议总结', [
                    'article_id' => $article->id,
                    'title'      => $article->title
                ]);

                // 获取文章内容
                $content = $article->getContentData();

                if ($content) {
                    // 流式输出总结内容
                    $this->streamText($content);

                    // 发送完成事件
                    $this->sendSSEEvent('complete', '会议总结输出完成', [
                        'article_id' => $article->id,
                        'success'    => true
                    ]);
                } else {
                    $this->sendSSEEvent('error', '暂无会议总结内容');
                    $this->sendSSEEvent('complete', '处理完成', [
                        'article_id' => $article->id,
                        'success'    => false
                    ]);
                }
            } catch (Exception $e) {
                $this->sendSSEEvent('error', '系统异常：'.$e->getMessage());
                $this->sendSSEEvent('complete', '处理完成', [
                    'article_id' => $article->id,
                    'success'    => false
                ]);
            }
        });
    }

    /**
     * 流式输出文本内容
     *
     * @param  string  $text
     */
    private function streamText(string $text): void
    {
        // 将文本按字符分割（支持中文）
        $chars = mb_str_split($text, 1, 'UTF-8');

        foreach ($chars as $char) {
            $this->sendSSEEvent('msg', $char);
            // 添加小延迟模拟打字效果
            usleep(30000); // 30毫秒
        }
    }

    /**
     * 发送SSE事件
     *
     * @param  string  $type
     * @param  string  $message
     * @param  array  $data
     */
    private function sendSSEEvent(string $type, string $message = '', array $data = []): void
    {
        echo 'data: '.json_encode([
                'type' => $type,
                'msg'  => $message,
                'data' => $data,
            ], JSON_UNESCAPED_UNICODE)."\n\n";
        ob_flush();
        flush();
        usleep(2000);
    }

    /**
     * 将转录数据转换为会议段落格式
     *
     * @param  array  $textRecord
     * @return array
     */
    private function convertTextRecordToParagraphs(array $textRecord): array
    {
        $paragraphs = [];

        foreach ($textRecord as $index => $record) {
            if (! isset($record['text']) || empty(trim($record['text']))) {
                continue;
            }

            $paragraph = [
                'ws' => trim($record['text']), // 段落文本内容（必传）
                'pt' => (string) $index,        // 段落编号（必传）
            ];

            // 可选字段
            if (isset($record['start'])) {
                $paragraph['pg'] = (string) $record['start']; // 段首时间(ms)
            }

            if (isset($record['end'])) {
                $paragraph['pd'] = (string) $record['end']; // 段尾时间(ms)
            }

            if (isset($record['speaker'])) {
                $paragraph['prl'] = (string) $record['speaker']; // 角色ID
            }

            $paragraphs[] = $paragraph;
        }

        return $paragraphs;
    }

    /**
     * 处理讯飞CKM异常
     */
    private function handleXfyunCkmException(XfyunCkmException $e, int $articleId): void
    {
        $errorDetails               = $e->getErrorDetails();
        $errorDetails['article_id'] = $articleId;

        if ($e->isConfigError()) {
            $this->logger->error('讯飞CKM配置错误，请检查环境变量配置', $errorDetails);
        } elseif ($e->isValidationError()) {
            $this->logger->error('讯飞CKM数据验证失败，请检查转录数据格式', $errorDetails);
        } elseif ($e->isAuthError()) {
            $this->logger->error('讯飞CKM认证失败，请检查appId、accessKey等配置', $errorDetails);
        } elseif ($e->isParameterError()) {
            $this->logger->error('讯飞CKM参数错误，请检查请求参数', $errorDetails);
        } elseif ($e->isTimeoutError()) {
            $this->logger->warning('讯飞CKM请求超时，建议稍后重试', array_merge($errorDetails, [
                'timeout_type' => '查询轮询超时',
                'suggestion'   => '会议录音较长或服务繁忙，建议稍后重试或联系管理员'
            ]));
        } elseif ($e->isResponseError()) {
            $this->logger->error('讯飞CKM响应解析失败，可能是服务端问题', $errorDetails);
        } elseif ($e->isRetryable()) {
            $this->logger->warning('讯飞CKM服务暂时不可用，可稍后重试', $errorDetails);
        } else {
            $this->logger->error('讯飞CKM服务调用失败', $errorDetails);
        }
    }

    /**
     * 保存会议总结到笔记
     *
     * @param  BailianArticle  $article
     * @param  array  $summaryResult
     */
    private function saveMeetingSummary(BailianArticle $article, array $summaryResult): void
    {
        $summaryParts = [];

        // 0. 添加会议信息
        $meetingInfo = $this->generateMeetingInfo($article);
        if ($meetingInfo) {
            $summaryParts[] = $meetingInfo;
        }
        // 1. 添加智能总结
        if (isset($summaryResult['textSummary']['summary']) && ! empty($summaryResult['textSummary']['summary'])) {
            $summaryParts[] = "### 会议总结\n\n".$summaryResult['textSummary']['summary'];
        }

        // 2. 添加会议要点
        if (isset($summaryResult['textSummary']['highlights']) && ! empty($summaryResult['textSummary']['highlights'])) {
            $highlights     = implode("\n- ", $summaryResult['textSummary']['highlights']);
            $summaryParts[] = "### 会议要点\n\n- ".$highlights;
        }

        // 3. 添加章节概要（包含时间信息）
        if (isset($summaryResult['senseTitle']['titles'], $summaryResult['senseSummary']['summarys']) && ! empty($summaryResult['senseTitle']['titles'])) {
            $chapterParts = [];
            $senseSplits  = $summaryResult['senseSplit']['ps'] ?? [];

            foreach ($summaryResult['senseTitle']['titles'] as $index => $chapterTitle) {
                if (isset($summaryResult['senseSummary']['summarys'][$index])) {
                    $chapterSummary = $summaryResult['senseSummary']['summarys'][$index];

                    // 获取时间信息
                    $timeInfo = '';
                    if (isset($senseSplits[$index])) {
                        $split = $senseSplits[$index];
                        if (isset($split['pg'])) {
//                            $startTime = $this->formatTimeToHMS((int) $split['pg']);
                            $startTime = '##### '.$split['pg'];
                            $timeInfo  = $startTime.' ';
                        }
                    }

                    $chapterParts[] = $timeInfo.$chapterTitle."\n\n".$chapterSummary;
                }
            }

            if (! empty($chapterParts)) {
                $summaryParts[] = "### 章节摘要\n\n".implode("\n\n", $chapterParts);
            }
        }

        // 4. 添加待办事项
        $agentsList = $summaryResult['meetingAgent']['agents'] ?? $summaryResult['meetingAgent'] ?? [];
        if (! empty($agentsList)) {
            $agents         = implode("\n- ", $agentsList);
            $summaryParts[] = "### 待办事项\n\n- ".$agents;
        }

        // 5. 更新标题
        $title = $article->title;
        if (isset($summaryResult['senseTitle']['titles']) && ! empty($summaryResult['senseTitle']['titles'])) {
            $title = $summaryResult['senseTitle']['titles'][0];
        }

        // 6. 保存到数据库
        if (! empty($summaryParts)) {
            $data = implode("\n\n", $summaryParts);

            $content = $article->content;
            if (is_string($content)) {
                $content = json_decode($content, true);
            }
            $content['data'] = $data;

            // 添加会议信息到content中
            $content['meeting_info'] = $this->extractMeetingInfo($article);
            $description             = $article->generateDescription($data);

            $article->update([
                'title'                   => $title,
                'content'                 => $content,
                'description'             => $description,
                'processing_status'       => ArticleProcessingStatusEnum::COMPLETED,
                'processing_completed_at' => now(),
                'processing_error'        => null
            ]);

            $contentText = $article->getStripHtmlData();
            dispatch(new GenerateArticleTagsJob($article, $contentText));

            $this->logger->info('会议总结已保存到笔记', [
                'article_id'     => $article->id,
                'content_length' => mb_strlen($data)
            ]);

            // 如果会议笔记已加入知识库，触发同步事件
            $this->triggerKnowledgeSync($article);
        }
    }

    /**
     * 生成会议信息部分
     *
     * @param  BailianArticle  $article
     * @return string|null
     */
    private function generateMeetingInfo(BailianArticle $article): ?string
    {
        $content = $article->content;
        if (is_string($content)) {
            $content = json_decode($content, true);
        }

        $meetingInfo = [];

        // 会议时间
        if (isset($content['meeting_info']['start_at'], $content['meeting_info']['end_at'])) {
            $startTime     = $content['meeting_info']['start_at'];
            $endTime       = $content['meeting_info']['end_at'];
            $meetingInfo[] = "• **会议时间**: {$startTime} ~ {$endTime}";
        } else {
            // 如果没有设置会议时间，使用文章创建时间
            $createdAt     = $article->created_at->format('Y-m-d H:i:s');
            $meetingInfo[] = "• **会议时间**: {$createdAt}";
        }

        // 会议时长
        if (isset($content['audioDuration'])) {
            $duration      = $this->formatDuration($content['audioDuration']);
            $meetingInfo[] = "• **时长**: {$duration}";
        }
        // 参与人数
        if (isset($content['meeting_info']['members'])) {
            $members       = $content['meeting_info']['members'];
            $meetingInfo[] = "• **参与人数**: 约{$members}人";
        } else {
            // 从text_record中统计说话人数量
            $speakerCount = $this->countSpeakers($content);
            if ($speakerCount > 0) {
                $meetingInfo[] = "• **参与人数**: 约{$speakerCount}人";
            }
        }

        // 场景类型
        $meetingInfo[] = "• **场景类型**: 未明确";

        if (! empty($meetingInfo)) {
            return "### 会议信息\n\n".implode("\n", $meetingInfo);
        }

        return null;
    }

    /**
     * 处理会议笔记总结
     * 从text_record中提取转录文本，调用讯飞CKM服务生成总结
     *
     * @param  BailianArticle  $article
     * @return array|null
     * @throws XfyunCkmException
     */
    public function generateMeetingSummary(BailianArticle $article): ?array
    {
        // 检查是否为会议录音类型的笔记
        if ($article->type !== BailianArticleTypeEnum::MEETING) {
            $this->logger->info('非会议录音类型笔记，跳过会议总结', [
                'article_id' => $article->id,
                'type'       => $article->type->value
            ]);
            return null;
        }

        try {
            // 解析content数据
            $content = $article->content;
            if (is_string($content)) {
                $content = json_decode($content, true);
            }

            // 检查是否有text_record数据
            if (! isset($content['text_record']) || empty($content['text_record'])) {
                $this->logger->info('笔记没有转录数据，跳过会议总结', [
                    'article_id' => $article->id
                ]);
                return null;
            }

            // 解析转录数据
            $textRecord = $content['text_record'];
            if (is_string($textRecord)) {
                $textRecord = json_decode($textRecord, true);
            }

            if (! is_array($textRecord) || empty($textRecord)) {
                $this->logger->warning('转录数据格式错误', [
                    'article_id'  => $article->id,
                    'text_record' => $content['text_record']
                ]);
                return null;
            }

            // 转换为会议段落格式
            $paragraphs = $this->convertTextRecordToParagraphs($textRecord);

            if (empty($paragraphs)) {
                $this->logger->info('转录数据为空，跳过会议总结', [
                    'article_id' => $article->id
                ]);
                return null;
            }

            $this->logger->info('开始调用讯飞CKM服务生成会议总结', [
                'article_id'        => $article->id,
                'paragraphs_count'  => count($paragraphs),
                'total_text_length' => array_sum(array_map(fn($p) => mb_strlen($p['ws'] ?? ''), $paragraphs))
            ]);

            $callbackUrl = 'https://ai.api.watestar.com/api/callback/xfyun_ckm';
            $startTime   = microtime(true);

            // 调用讯飞CKM服务进行会议总结
            $result = XfyunCkm::aggregate()->requestComplete($paragraphs, $callbackUrl);

            $this->logger->info('讯飞CKM服务返回结果', [
                'article_id' => $article->id,
                'result'     => $result
            ]);

            // 轮询查询结果（使用优化的查询方法）
            $taskId      = $result['taskId'];
            $queryResult = XfyunCkm::query()->getQueryResult($taskId);

            $this->logger->info('讯飞CKM查询结果', [
                'article_id'   => $article->id,
                'task_id'      => $taskId,
                'query_result' => $queryResult
            ]);

            if ($queryResult['isSuccess']) {
                // 保存总结到笔记的description字段
                $this->saveMeetingSummary($article, $queryResult);
                return $queryResult;
            } else {
                $this->logger->error('会议总结生成失败', [
                    'article_id' => $article->id,
                    'task_id'    => $taskId,
                    'result'     => $queryResult
                ]);
                return null;
            }
        } catch (XfyunCkmException $e) {
            $this->handleXfyunCkmException($e, $article->id);
            throw $e;
        } catch (Exception $e) {
            $this->logger->error('会议总结生成异常', [
                'article_id' => $article->id,
                'error'      => $e->getMessage(),
                'file'       => $e->getFile(),
                'line'       => $e->getLine()
            ]);
            throw $e;
        }
    }

    /**
     * 提取会议信息数据
     *
     * @param  BailianArticle  $article
     * @return array
     */
    private function extractMeetingInfo(BailianArticle $article): array
    {
        $content = $article->content;
        if (is_string($content)) {
            $content = json_decode($content, true);
        }

        $meetingInfo = [
            'start_at' => $content['meeting_info']['start_at'] ?? $article->created_at->format('Y-m-d H:i:s'),
            'end_at'   => $content['meeting_info']['end_at'] ?? null,
            'members'  => $content['meeting_info']['members'] ?? $this->countSpeakers($content),
        ];

        // 如果没有结束时间，根据开始时间和音频时长计算
        if (! $meetingInfo['end_at'] && isset($content['audioDuration'])) {
            $startTime = new \DateTime($meetingInfo['start_at']);
            $startTime->add(new \DateInterval('PT'.(int) $content['audioDuration'].'S'));
            $meetingInfo['end_at'] = $startTime->format('Y-m-d H:i:s');
        }

        return $meetingInfo;
    }

    /**
     * 统计说话人数量
     *
     * @param  array  $content
     * @return int
     */
    private function countSpeakers(array $content): int
    {
        if (! isset($content['text_record'])) {
            return 1;
        }

        $textRecord = $content['text_record'];
        if (is_string($textRecord)) {
            $textRecord = json_decode($textRecord, true);
        }

        if (! is_array($textRecord)) {
            return 1;
        }

        $speakers = [];
        foreach ($textRecord as $record) {
            if (isset($record['speaker'])) {
                $speakers[$record['speaker']] = true;
            }
        }

        return max(1, count($speakers));
    }

    /**
     * 格式化时长（秒转为分钟格式）
     *
     * @param  int  $seconds
     * @return string
     */
    public function formatDuration(int $seconds): string
    {
        $minutes          = intval($seconds / 60);
        $remainingSeconds = $seconds % 60;

        if ($minutes > 0) {
            return "约{$minutes}分钟";
        } else {
            return "约{$remainingSeconds}秒";
        }
    }

    /**
     * 格式化时间（毫秒转为 HH:MM:SS 格式）
     *
     * @param  int  $milliseconds
     * @return string
     */
    private function formatTimeToHMS(int $milliseconds): string
    {
        $totalSeconds = intval($milliseconds / 1000);
        $hours        = intval($totalSeconds / 3600);
        $minutes      = intval(($totalSeconds % 3600) / 60);
        $seconds      = $totalSeconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    /**
     * 格式化时间（毫秒转为 MM:SS 格式）
     *
     * @param  int  $milliseconds
     * @return string
     */
    private function formatTime(int $milliseconds): string
    {
        $seconds = intval($milliseconds / 1000);
        $minutes = intval($seconds / 60);
        $seconds = $seconds % 60;

        return sprintf('%02d:%02d', $minutes, $seconds);
    }

    /**
     * 触发会议笔记的知识库同步
     */
    private function triggerKnowledgeSync(BailianArticle $article): void
    {
        // 检查是否为会议笔记
        if ($article->type !== BailianArticleTypeEnum::MEETING) {
            return;
        }

        // 检查是否已加入知识库
        $knowledgeItem = $article->knowledgeItem;
        if (! $knowledgeItem) {
            return;
        }
        $knowledgeItem->update([
            'title' => $article->title
        ]);

        // 手动触发知识库同步事件
        event(new KnowledgeItemCreatedEvent($knowledgeItem));

        $this->logger->info('会议笔记总结完成，已触发知识库同步', [
            'article_id'        => $article->id,
            'knowledge_item_id' => $knowledgeItem->id
        ]);
    }

    /**
     * 生成会议总结（支持多种AI服务）
     */
    public function generateMeetingSummaryWithProvider(BailianArticle $article, string $provider = 'doubao'): array
    {
        switch ($provider) {
            case 'doubao':
                return $this->generateSummaryWithDoubao($article);
            case 'xfyun':
                return $this->submitToXfyunCkm($article);
            default:
                return ['success' => false, 'message' => '不支持的AI服务提供商'];
        }
    }

    /**
     * 使用豆包AI生成会议总结
     */
    public function generateSummaryWithDoubao(BailianArticle $article): array
    {
        try {
            $content = $article->content;
            if (is_string($content)) {
                $content = json_decode($content, true);
            }

            $textRecord = $content['text_record'] ?? '';
            $meetingInfo = $content['meeting_info'] ?? [];

            if (empty($textRecord)) {
                throw new Exception('会议转录数据为空');
            }

            // 构建提示词
            $prompt = $this->buildDoubaoPrompt($textRecord, $meetingInfo);

            // 调用豆包API
            $result = $this->callDoubaoApi($prompt);

            if ($result['success']) {
                // 解析返回的结构化数据
                $summaryData = $this->parseDoubaoResponse($result['content']);

                // 保存到文章
                $this->saveDoubaoSummary($article, $summaryData);

                $this->logger->info('豆包AI会议总结生成成功', [
                    'article_id' => $article->id
                ]);

                return ['success' => true, 'message' => '会议总结生成成功'];
            } else {
                throw new Exception($result['message'] ?? '豆包API调用失败');
            }

        } catch (Exception $e) {
            $this->logger->error('豆包AI会议总结生成失败', [
                'article_id' => $article->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 构建豆包AI的提示词
     */
    private function buildDoubaoPrompt(string $textRecord, array $meetingInfo): string
    {
        $startTime = $meetingInfo['start_at'] ?? '';
        $endTime = $meetingInfo['end_at'] ?? '';

        return <<<EOT
{$textRecord}
本次会议开始时间：{$startTime}
本次会议结束时间：{$endTime}

这是一段json文件，描述了会议中各个角色的讲话内容。
start 和 end 表示讲话的开始和结束，text 表示讲话内容，speaker 表示讲话者的编号。
章节摘要:需要你阅读每个人的说话在内容对每一个章节，并进行一个总结。
请你按以下格式总结并返回这段json文件的内容。
内容用markdown返回。
{
    'meeting_topics': {
            'meeting_time' : '{$startTime} - {$endTime}',
            'speaker': '*',
            'topic': '*',
            'duration': '*分钟'
        },
        'content':'内容总结',
        'meeting_summary':'会议总结',
        'todo':'代办事项',
        'chapter_summary':'章节摘要'
    ],
}
EOT;
    }

    /**
     * 调用豆包API（带重试机制）
     */
    private function callDoubaoApi(string $prompt): array
    {
        $maxRetries = 2;
        $retryDelay = 5; // 秒

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            $result = $this->makeDoubaoApiCall($prompt);

            if ($result['success']) {
                return $result;
            }

            // 如果不是最后一次尝试，等待后重试
            if ($attempt < $maxRetries) {
                $this->logger->warning("豆包API调用失败，{$retryDelay}秒后重试", [
                    'attempt' => $attempt,
                    'error' => $result['message']
                ]);
                sleep($retryDelay);
            }
        }

        return $result; // 返回最后一次的结果
    }

    /**
     * 执行豆包API调用
     */
    private function makeDoubaoApiCall(string $prompt): array
    {
        $url = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer d7559878-baec-4a6f-8d76-8d7d04a5582e',
        ];

        $payload = json_encode([
            'model' => 'ep-20250611165125-7dhbg',
            'messages' => [
                [
                    'content' => [
                        [
                            'text' => $prompt,
                            'type' => 'text'
                        ]
                    ],
                    'role' => 'user'
                ]
            ],
            'stream' => false // 非流式返回，获取完整结果
        ]);

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 300);        // 总超时时间：5分钟
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);  // 连接超时：1分钟
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 跳过SSL验证（如果有证书问题）

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            return ['success' => false, 'message' => "CURL错误: {$error}"];
        }

        curl_close($ch);

        if ($httpCode !== 200) {
            return ['success' => false, 'message' => "HTTP错误: {$httpCode}", 'response' => $response];
        }

        $data = json_decode($response, true);
        if (!$data || !isset($data['choices'][0]['message']['content'])) {
            return ['success' => false, 'message' => '响应格式错误', 'response' => $response];
        }

        return [
            'success' => true,
            'content' => $data['choices'][0]['message']['content']
        ];
    }

    /**
     * 解析豆包AI的响应
     */
    private function parseDoubaoResponse(string $content): array
    {
        // 豆包返回的是markdown格式的结构化内容，直接按章节解析
        $sections = [
            'meeting_topics' => '',
            'content' => '',
            'meeting_summary' => '',
            'todo' => '',
            'chapter_summary' => ''
        ];

        // 解析会议主题部分（包含表格）
        if (preg_match('/### 会议主题\s*(.*?)(?=###|$)/s', $content, $matches)) {
            $sections['meeting_topics'] = trim($matches[1]);
        }

        // 解析内容总结
        if (preg_match('/### 内容总结\s*(.*?)(?=###|$)/s', $content, $matches)) {
            $sections['content'] = trim($matches[1]);
        }

        // 解析会议总结
        if (preg_match('/### 会议总结\s*(.*?)(?=###|$)/s', $content, $matches)) {
            $sections['meeting_summary'] = trim($matches[1]);
        }

        // 解析代办事项
        if (preg_match('/### 代办事项\s*(.*?)(?=###|$)/s', $content, $matches)) {
            $sections['todo'] = trim($matches[1]);
        }

        // 解析章节摘要
        if (preg_match('/### 章节摘要\s*(.*?)(?=###|$)/s', $content, $matches)) {
            $sections['chapter_summary'] = trim($matches[1]);
        }

        return $sections;
    }

    /**
     * 保存豆包AI生成的会议总结
     */
    private function saveDoubaoSummary(BailianArticle $article, array $summaryData): void
    {
        // 构建markdown格式的会议总结
        $markdown = $this->buildMarkdownSummary($summaryData);

        // 生成标题和描述
        $title = "会议记录 - " . date('Y-m-d H:i', strtotime($article->created_at));
        $description = mb_substr(strip_tags($summaryData['meeting_summary'] ?? ''), 0, 200);

        // 更新文章
        $article->update([
            'title' => $title,
            'content' => array_merge($article->content, ['data' => $markdown]),
            'description' => $description,
            'processing_status' => ArticleProcessingStatusEnum::COMPLETED,
            'processing_completed_at' => now(),
            'processing_error' => null
        ]);

        // 生成标签
        $contentText = $article->getStripHtmlData();
        dispatch(new GenerateArticleTagsJob($article, $contentText));

        // 触发知识库同步
        $this->triggerKnowledgeSync($article);
    }

    /**
     * 构建markdown格式的会议总结（统一格式）
     */
    private function buildMarkdownSummary(array $summaryData): string
    {
        $markdown = "## 会议信息\n\n";

        // 处理会议主题信息，转换表格为文本格式
        $meetingInfo = $summaryData['meeting_topics'];
        if (strpos($meetingInfo, '|') !== false) {
            // 如果是表格格式，提取数据行（跳过表头和分隔符）
            $lines = explode("\n", $meetingInfo);
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line) || strpos($line, '会议时间') !== false || strpos($line, '---') !== false) {
                    continue;
                }

                if (preg_match('/\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|/', $line, $matches)) {
                    $time = trim($matches[1]);
                    $topic = trim($matches[2]);
                    $duration = trim($matches[3]);

                    $markdown .= "• **会议时间**: {$time}\n";
                    $markdown .= "• **会议主题**: {$topic}\n";
                    $markdown .= "• **会议时长**: {$duration}\n";
                    $markdown .= "• **参与人数**: 约3人\n";
                    $markdown .= "• **场景类型**: 项目讨论\n\n";
                    break;
                }
            }
        } else {
            $markdown .= $meetingInfo . "\n\n";
        }

        $markdown .= "## 会议总结\n\n";
        $markdown .= $this->cleanContent($summaryData['meeting_summary']) . "\n\n";

        $markdown .= "## 会议要点\n\n";
        $markdown .= $this->cleanContent($summaryData['content']) . "\n\n";

        $markdown .= "## 章节摘要\n\n";
        $markdown .= $this->formatChapterSummary($summaryData['chapter_summary']) . "\n\n";

        $markdown .= "## 待办事项\n\n";
        $markdown .= $this->formatTodoItems($summaryData['todo']) . "\n\n";

        return $markdown;
    }

    /**
     * 清理内容中的特殊字符
     */
    private function cleanContent(string $content): string
    {
        // 移除ANSI转义序列
        $content = preg_replace('/\x1b\[[0-9;]*[mK]/', '', $content);
        // 移除多余的换行
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        return trim($content);
    }

    /**
     * 格式化章节摘要
     */
    private function formatChapterSummary(string $chapterSummary): string
    {
        $cleaned = $this->cleanContent($chapterSummary);

        // 如果是表格格式，转换为章节格式
        if (strpos($cleaned, '|') !== false) {
            $lines = explode("\n", $cleaned);
            $formatted = '';
            $chapterNum = 1;

            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line) || strpos($line, '讲话人') !== false || strpos($line, '---') !== false) {
                    continue;
                }

                if (preg_match('/\|\s*(\d+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|/', $line, $matches)) {
                    $speaker = trim($matches[1]);
                    $duration = trim($matches[2]);
                    $summary = trim($matches[3]);

                    $formatted .= "##### {$chapterNum} 发言人{$speaker}\n\n";
                    $formatted .= "{$summary}\n\n";
                    $chapterNum++;
                }
            }
            return $formatted;
        }

        return $cleaned;
    }

    /**
     * 格式化待办事项
     */
    private function formatTodoItems(string $todoItems): string
    {
        $cleaned = $this->cleanContent($todoItems);

        // 确保每个待办事项都以 "- " 开头
        $lines = explode("\n", $cleaned);
        $formatted = '';

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // 如果是数字列表，转换为破折号列表
            $line = preg_replace('/^\d+\.\s*\*\*([^*]+)\*\*:\s*/', '- **$1**: ', $line);

            if (!str_starts_with($line, '-')) {
                $line = '- ' . $line;
            }

            $formatted .= $line . "\n";
        }

        return $formatted;
    }
}
