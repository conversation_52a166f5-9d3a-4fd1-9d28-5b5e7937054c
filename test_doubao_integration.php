<?php

// 简单测试豆包API调用

// 测试数据
$textRecord = '[
    {
        "start": 1000,
        "end": 5000,
        "text": "大家好，今天我们开会讨论项目进度。",
        "speaker": 1
    },
    {
        "start": 6000,
        "end": 12000,
        "text": "目前前端开发已经完成了80%，预计下周可以完成。",
        "speaker": 2
    },
    {
        "start": 13000,
        "end": 18000,
        "text": "后端API接口还需要优化，预计需要3天时间。",
        "speaker": 1
    },
    {
        "start": 19000,
        "end": 25000,
        "text": "测试工作安排在下周二开始，大家准备好测试用例。",
        "speaker": 3
    }
]';

$startTime = '2025-06-26 10:00:00';
$endTime = '2025-06-26 11:00:00';

// 构建提示词
$prompt = <<<EOT
{$textRecord}
本次会议开始时间：{$startTime}
本次会议结束时间：{$endTime}

这是一段json文件，描述了会议中各个角色的讲话内容。
start 和 end 表示讲话的开始和结束，text 表示讲话内容，speaker 表示讲话者的编号。
章节摘要:需要你阅读每个人的说话在内容对每一个章节，并进行一个总结。
请你按以下格式总结并返回这段json文件的内容。
内容用markdown返回。
{
    'meeting_topics': {
            'meeting_time' : '{$startTime} - {$endTime}',
            'speaker': '*',
            'topic': '*',
            'duration': '*分钟'
        },
        'content':'内容总结',
        'meeting_summary':'会议总结',
        'todo':'代办事项',
        'chapter_summary':'章节摘要'
    ],
}
EOT;

echo "=== 豆包AI会议总结测试 ===\n\n";
echo "1. 提示词构建完成\n";
echo "提示词长度: " . strlen($prompt) . " 字符\n\n";

echo "2. 开始调用豆包API...\n";

// 调用豆包API
function callDoubaoApi($prompt) {
    $url = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer d7559878-baec-4a6f-8d76-8d7d04a5582e',
    ];

    $payload = json_encode([
        'model' => 'ep-20250611165125-7dhbg',
        'messages' => [
            [
                'content' => [
                    [
                        'text' => $prompt,
                        'type' => 'text'
                    ]
                ],
                'role' => 'user'
            ]
        ],
        'stream' => false
    ]);

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        return ['success' => false, 'message' => "CURL错误: {$error}"];
    }

    curl_close($ch);

    if ($httpCode !== 200) {
        return ['success' => false, 'message' => "HTTP错误: {$httpCode}", 'response' => $response];
    }

    $data = json_decode($response, true);
    if (!$data || !isset($data['choices'][0]['message']['content'])) {
        return ['success' => false, 'message' => '响应格式错误', 'response' => $response];
    }

    return [
        'success' => true,
        'content' => $data['choices'][0]['message']['content']
    ];
}

$result = callDoubaoApi($prompt);

echo "API调用结果: " . ($result['success'] ? '成功' : '失败') . "\n";

if ($result['success']) {
    echo "响应内容长度: " . strlen($result['content']) . " 字符\n";
    echo "响应内容:\n" . $result['content'] . "\n\n";
} else {
    echo "API调用失败: " . $result['message'] . "\n";
    if (isset($result['response'])) {
        echo "原始响应: " . $result['response'] . "\n";
    }
}

echo "=== 测试完成 ===\n";
