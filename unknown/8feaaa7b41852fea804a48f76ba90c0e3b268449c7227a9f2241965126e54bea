<?php

namespace Modules\Payment\Drivers\PaymentAdapters;

use GuzzleHttp\Psr7\Response;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;

class PaypalAdapter implements PaymentAdapter
{
    private string $apiUrl;
    private bool   $sandbox;
    private string $businessEmail;

    private array $config;

    public function __construct(protected Payment|null $payment)
    {
        if (config('payment.GATEWAY_CHANNEL_PAYPAL') == 'sandbox') {
            $this->apiUrl        = 'https://www.sandbox.paypal.com/cgi-bin/webscr';
            $this->businessEmail = config('payment.PAYPAL_SAND_EMAIL');
            $this->sandbox       = true;
        } else {
            $this->apiUrl        = 'https://www.paypal.com/cgi-bin/webscr';
            $this->businessEmail = config('payment.PAYPAL_ACCOUNT_EMAIL');
            $this->sandbox       = false;
        }

        $this->config = $this->getConfig();
    }

    public function getConfig(): array
    {
        return [
            'url'  => $this->apiUrl,
            'data' => [
                'cmd'           => '_xclick',
                'business'      => $this->businessEmail,
                'item_name'     => $this->payment?->paymentable->getTitle(),
                'item_number'   => $this->payment?->no,
                'amount'        => $this->payment?->amount,
                'shipping'      => '0.00',
                'currency_code' => $this->payment?->currency,
                'return'        => $this->payment ? route('payment.return', [
                    'payment' => $this?->payment,
                ]) : '',
                'notify_url'    => $this->payment ? route('api.payment.notify', [
                    'gateway' => $this->payment?->gateway->value,
                ]) : '',
                'cancel_return' => $this->payment ? route('payment.cancel', [
                    'payment' => $this->payment,
                ]) : '',
                'invoice'       => $this->payment?->no,
                'no_note'       => $this->payment?->paymentable->getTitle(),
            ]
        ];
    }

    public function isSandBox(): bool
    {
        return $this->sandbox;
    }

    public function web(): array
    {
        return $this->config;
    }

    public function callback(): array
    {
        try {
            $data        = request()->post();
            $data['cmd'] = '_notify-validate';
            $res         = $this->https_request($this->apiUrl, $data);
            if (! empty($res) && strcmp($res, "VERIFIED") == 0) {
                $data['code']    = 1;
                $data['message'] = '回调验证成功';
                return $data;
            }
            return [
                'code'    => 0,
                'message' => '验证失败',
            ];
        } catch (\Exception $e) {
            return [
                'code'    => 0,
                'message' => $e->getMessage(),
            ];
        }
    }

    private function https_request($url, $data = [])
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $tmpInfo = curl_exec($ch);
        if (curl_errno($ch)) {
            return curl_error($ch);
        }

        curl_close($ch);
        return $tmpInfo;
    }

    public function success(): ResponseInterface
    {
        return new Response(200, [], [
            'code'    => 'success',
            'message' => '执行成功',
        ]);
    }

    public function error(string $message): ResponseInterface
    {
        return new Response(400, [], [
            'code'    => 'error',
            'message' => $message,
        ]);
    }

    public function query(): array
    {
        return [];
    }

    public function refund(string $refundNo, float $refundAmount, string $desc = ''): array
    {
        return [];
    }

}