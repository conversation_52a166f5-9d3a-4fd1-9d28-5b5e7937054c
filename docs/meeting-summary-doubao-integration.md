# 会议笔记豆包AI集成技术文档

## 📋 概述

本文档描述了将豆包AI（火山引擎）集成到会议笔记系统中的完整技术方案，实现会议录音的智能总结功能。

## 🎯 业务目标

- 提升会议总结质量（豆包AI中文理解能力更强）
- 提供双重保障机制（豆包AI + 讯飞CKM备选）
- 统一输出格式（与现有讯飞格式保持一致）
- 优化用户体验（异步处理，避免长时间等待）

## 🏗️ 系统架构

### 当前架构（已实现）
```
用户创建会议笔记 → 同步调用豆包AI（5分钟） → 成功/失败处理
                                    ↓ 失败
                              回退到讯飞CKM（异步）
```

### 目标架构（待实现）
```
用户创建会议笔记 → 立即返回成功 → 后台队列处理豆包AI
前端轮询状态 → 显示处理进度 → 完成后获取结果
                              ↓ 失败
                         自动回退到讯飞CKM
```

## 🔧 技术实现

### 1. 核心服务类

#### MeetingSummaryService
- **位置**: `app/Services/MeetingSummaryService.php`
- **新增方法**:
  - `generateSummaryWithDoubao()` - 豆包AI总结生成
  - `callDoubaoApi()` - API调用（含重试机制）
  - `parseDoubaoResponse()` - 响应解析
  - `buildMarkdownSummary()` - 格式转换

### 2. API配置

```php
// 豆包AI配置
$url = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
$model = 'ep-20250611165125-7dhbg';
$token = 'd7559878-baec-4a6f-8d76-8d7d04a5582e';

// 超时设置
$timeout = 300; // 5分钟
$connectTimeout = 60; // 1分钟
$maxRetries = 2; // 最大重试次数
```

### 3. 数据流转

#### 输入格式
```json
{
  "text_record": "[{\"start\":1000,\"end\":5000,\"text\":\"会议内容\",\"speaker\":1}]",
  "meeting_info": {
    "start_at": "2025-06-26 10:00:00",
    "end_at": "2025-06-26 11:00:00", 
    "members": 3
  }
}
```

#### 豆包AI返回格式
```markdown
### 会议主题
| 会议时间 | 会议主题 | 会议时长 |
|----------|----------|----------|
| 2025-06-26 10:00:00 - 11:00:00 | 项目讨论 | 60分钟 |

### 会议总结
会议主要内容...

### 代办事项
1. **前端团队**: 完成开发任务
2. **后端团队**: 优化API接口
```

#### 统一输出格式
```markdown
## 会议信息
• **会议时间**: 2025-06-26 10:00:00 - 11:00:00
• **会议主题**: 项目讨论
• **会议时长**: 60分钟

## 会议总结
会议主要内容...

## 会议要点
- 主要讨论点...

## 章节摘要
##### 1 发言人1
发言内容摘要...

## 待办事项
- **前端团队**: 完成开发任务
- **后端团队**: 优化API接口
```

### 4. 状态管理

#### 处理状态枚举
```php
ArticleProcessingStatusEnum::PENDING    // 待处理
ArticleProcessingStatusEnum::PROCESSING // 处理中
ArticleProcessingStatusEnum::COMPLETED  // 已完成
ArticleProcessingStatusEnum::FAILED     // 失败
```

#### 状态判断方法
```php
// app/Models/Traits/ArticleBusinessTrait.php
public function getMeetingSummaryStatus(): array
{
    return [
        'has_summary' => strpos($summary, '## 会议总结') !== false,
        'has_agents' => strpos($summary, '## 待办事项') !== false,
        'has_paragraphs' => strpos($summary, '## 章节摘要') !== false,
    ];
}
```

## 🚀 待实现任务清单

### 1. 修改ArticleController创建逻辑
**文件**: `app/Http/Controllers/Api/Bailian/ArticleController.php`

**当前代码**:
```php
// 使用豆包AI生成会议总结（同步处理）
$result = $meetingSummaryService->generateMeetingSummaryWithProvider($article, 'doubao');
```

**目标代码**:
```php
// 异步提交豆包AI处理任务
dispatch(new GenerateDoubaoSummaryJob($article));
```

### 2. 创建豆包AI处理队列Job
**新建文件**: `app/Jobs/BaiLian/GenerateDoubaoSummaryJob.php`

```php
<?php
namespace App\Jobs\BaiLian;

use App\Models\BailianArticle;
use App\Services\MeetingSummaryService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class GenerateDoubaoSummaryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        private BailianArticle $article
    ) {}

    public function handle(): void
    {
        $service = new MeetingSummaryService();
        $result = $service->generateSummaryWithDoubao($this->article);
        
        if (!$result['success']) {
            // 豆包失败，回退到讯飞CKM
            $service->submitMeetingSummaryTask($this->article);
        }
    }
}
```

### 3. 队列配置
确保队列正常运行：
```bash
php artisan queue:work
```

### 4. 前端轮询逻辑
**API接口**: `GET /api/bailian/articles/{id}/query-meeting-summary`

**返回格式**:
```json
{
  "completed": true,
  "message": "会议总结已完成"
}
```

**前端逻辑**:
```javascript
// 创建会议笔记后开始轮询
const pollStatus = async (articleId) => {
  const response = await queryMeetingSummary(articleId);
  
  if (response.completed) {
    // 完成了，获取内容
    streamMeetingSummary(articleId);
  } else {
    // 继续轮询
    setTimeout(() => pollStatus(articleId), 5000);
  }
};
```

## 🔍 测试验证

### 1. 功能测试
- [ ] 会议笔记创建响应速度（<2秒）
- [ ] 豆包AI异步处理正常
- [ ] 失败回退到讯飞CKM
- [ ] 格式转换正确
- [ ] 知识库同步正常

### 2. 性能测试
- [ ] 并发创建会议笔记
- [ ] 队列处理能力
- [ ] 豆包API响应时间

### 3. 异常测试
- [ ] 豆包API超时处理
- [ ] 网络异常恢复
- [ ] 队列失败重试

## 📊 监控指标

### 关键指标
- 豆包AI成功率
- 平均处理时间
- 队列积压情况
- 回退到讯飞的比例

### 日志记录
```php
Log::info('豆包AI处理开始', ['article_id' => $article->id]);
Log::info('豆包AI处理完成', ['article_id' => $article->id, 'duration' => $duration]);
Log::warning('豆包AI失败，回退讯飞', ['article_id' => $article->id, 'error' => $error]);
```

## 🎯 优化建议

### 短期优化
1. 实现异步处理架构
2. 添加队列监控
3. 优化错误处理

### 长期优化
1. 支持更多AI服务商
2. 智能选择最优AI服务
3. 缓存常见会议模板
4. 支持自定义总结格式

## 📝 注意事项

1. **API密钥安全**: 豆包API密钥应存储在环境变量中
2. **队列监控**: 确保队列服务稳定运行
3. **错误处理**: 完善各种异常情况的处理
4. **用户体验**: 前端需要明确显示处理状态
5. **成本控制**: 监控API调用次数和费用

---

**文档版本**: v1.0  
**最后更新**: 2025-06-26  
**维护人员**: AI Assistant
