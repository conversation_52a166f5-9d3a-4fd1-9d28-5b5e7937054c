<div class="card dcat-box">
    <div class="box-header with-border" style="padding: .65rem 1rem">
        <h3 class="box-title" style="line-height:30px;">详细</h3>
        <div class="pull-right">
            <div class="btn-group pull-right btn-mini" style="margin-right: 5px">
                <button class="btn btn-sm btn-white " data-action="delete"
                        data-url="{{ route('admin.messages.destroy', $notification->id) }}"
                        data-redirect="{{ route('admin.messages.index') }}">
                    <i class="feather icon-trash"></i>
                    <span class="d-none d-sm-inline"> 删除</span>
                </button>
            </div>
            <div class="btn-group pull-right btn-mini" style="margin-right: 5px">
                <a href="{{ route('admin.messages.index') }}" class="btn btn-sm btn-primary ">
                    <i class="feather icon-list"></i>
                    <span class="d-none d-sm-inline"> 列表</span>
                </a>
            </div>
        </div>
    </div>
    <div class="box-body">
        <div class="form-horizontal mt-1">
            <div class="clearfix pl-4 pr-4">
                <div class="mt-2">
                    {{ $notification->data['content'] }}
                </div>
                <div class="mt-4">
                    发送时间：{{ $notification->created_at }}
                </div>
                <div class="mt-1">
                    阅读时间：{{ $notification->read_at }}
                </div>
            </div>
        </div>
    </div>
</div>